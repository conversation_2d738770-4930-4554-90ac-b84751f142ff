# PowerShell script to update PumpFunBuyer.cs
$filePath = "ChainTool\PumpFunBuyer.cs"
$content = Get-Content $filePath -Raw

# Replace the SellPumpFun method
$oldMethod = @'
    public void SellPumpFun()
    {

        // 1. 初始化钱包和客户端
        var wallet = new Wallet("你的助记词");

        // 2. 设置要交易的市场参数
        string pumpFunProgramId = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
        string tokenMint = "目标TokenMint地址";
        string marketAccount = "市场池地址";

        // 3. 你的 Token 账户
        var userTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(wallet.Account.PublicKey, new PublicKey(tokenMint));

        // 4. 构造买入指令（具体data参数和账户顺序需参考Pump.fun合约文档/Rust代码）
        var instruction = new TokenSwapProgram()
            .SetProgramId(pumpFunProgramId)
            .AddAccountMeta(wallet.Account.PublicKey, true, false) // 购买者
            .AddAccountMeta(new PublicKey(userTokenAccount), false, true) // 购买者的 token 账户
            .AddAccountMeta(new PublicKey(marketAccount), false, false) // 市场
            .AddInstructionData(new byte[] { /* Pump.fun特定指令数据 */ })
            .Build();

        // 5. 构建并发送交易
        var tx = new TransactionBuilder()
            .SetRecentBlockHash(rpcClient.GetLatestBlockHash().Result.Value.Blockhash)
            .SetFeePayer(wallet.Account)
            .AddInstruction(instruction)
            .Build(wallet.Account);

        var result = rpcClient.SendTransaction(tx);
        Console.WriteLine($"交易结果: {result.RawRpcResponse}");
    }
'@

$newMethod = @'
    /// <summary>
    /// 通过Pump.fun购买代币
    /// </summary>
    /// <param name="privateKey">钱包私钥</param>
    /// <param name="tokenMint">要购买的代币Mint地址</param>
    /// <param name="solAmount">要花费的SOL数量（以lamports为单位）</param>
    /// <param name="slippagePercent">滑点百分比（例如：5表示5%）</param>
    public void BuyPumpFun(string privateKey, string tokenMint, ulong solAmount, double slippagePercent = 5.0)
    {
        try
        {
            // 1. 创建钱包账户
            var account = new Account(privateKey, "");
            Console.WriteLine($"钱包地址: {account.PublicKey}");

            // 2. 设置参数
            var mint = new PublicKey(tokenMint);
            var feeRecipient = PumpFunProgram.GetDefaultFeeRecipient();
            
            // 计算滑点保护 - 这里需要根据bonding curve计算预期的token数量
            // 简化处理：假设1 SOL = 1000000 tokens（实际需要从bonding curve计算）
            ulong expectedTokenAmount = solAmount * 1000000; // 这是简化计算，实际需要查询bonding curve
            ulong maxSolCost = (ulong)(solAmount * (1 + slippagePercent / 100.0));

            // 3. 创建买入指令
            var buyInstruction = PumpFunProgram.CreateBuyInstruction(
                account.PublicKey,
                mint,
                expectedTokenAmount,
                maxSolCost,
                feeRecipient
            );

            // 4. 构建并发送交易
            var recentBlockHash = rpcClient.GetLatestBlockHash();
            var tx = new TransactionBuilder()
                .SetRecentBlockHash(recentBlockHash.Result.Value.Blockhash)
                .SetFeePayer(account)
                .AddInstruction(buyInstruction)
                .Build(account);

            var result = rpcClient.SendTransaction(tx);
            
            if (result.WasSuccessful)
            {
                Console.WriteLine($"购买成功！交易哈希: {result.Result}");
                Console.WriteLine($"花费SOL: {solAmount / 1_000_000_000.0:F4}");
                Console.WriteLine($"预期获得代币: {expectedTokenAmount}");
            }
            else
            {
                Console.WriteLine($"购买失败: {result.Reason}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"购买过程中出错: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
    }

    /// <summary>
    /// 通过Pump.fun出售代币
    /// </summary>
    /// <param name="privateKey">钱包私钥</param>
    /// <param name="tokenMint">要出售的代币Mint地址</param>
    /// <param name="tokenAmount">要出售的代币数量</param>
    /// <param name="slippagePercent">滑点百分比（例如：5表示5%）</param>
    public void SellPumpFun(string privateKey, string tokenMint, ulong tokenAmount, double slippagePercent = 5.0)
    {
        try
        {
            // 1. 创建钱包账户
            var account = new Account(privateKey, "");
            Console.WriteLine($"钱包地址: {account.PublicKey}");

            // 2. 设置参数
            var mint = new PublicKey(tokenMint);
            var feeRecipient = PumpFunProgram.GetDefaultFeeRecipient();
            
            // 计算滑点保护 - 这里需要根据bonding curve计算预期的SOL数量
            // 简化处理：假设1000000 tokens = 1 SOL（实际需要从bonding curve计算）
            ulong expectedSolAmount = tokenAmount / 1000000; // 这是简化计算，实际需要查询bonding curve
            ulong minSolOutput = (ulong)(expectedSolAmount * (1 - slippagePercent / 100.0));

            // 3. 创建卖出指令
            var sellInstruction = PumpFunProgram.CreateSellInstruction(
                account.PublicKey,
                mint,
                tokenAmount,
                minSolOutput,
                feeRecipient
            );

            // 4. 构建并发送交易
            var recentBlockHash = rpcClient.GetLatestBlockHash();
            var tx = new TransactionBuilder()
                .SetRecentBlockHash(recentBlockHash.Result.Value.Blockhash)
                .SetFeePayer(account)
                .AddInstruction(sellInstruction)
                .Build(account);

            var result = rpcClient.SendTransaction(tx);
            
            if (result.WasSuccessful)
            {
                Console.WriteLine($"出售成功！交易哈希: {result.Result}");
                Console.WriteLine($"出售代币数量: {tokenAmount}");
                Console.WriteLine($"预期获得SOL: {expectedSolAmount / 1_000_000_000.0:F4}");
            }
            else
            {
                Console.WriteLine($"出售失败: {result.Reason}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"出售过程中出错: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
    }
'@

$newContent = $content -replace [regex]::Escape($oldMethod), $newMethod
Set-Content $filePath $newContent -Encoding UTF8

Write-Host "PumpFunBuyer.cs has been updated successfully!"
