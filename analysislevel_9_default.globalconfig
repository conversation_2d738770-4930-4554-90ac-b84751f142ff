# NOTE: Requires **VS2019 16.7** or later

# Rules from '9.0' release with 'Default' analysis mode
# Description: Rules with enabled-by-default state from '9.0' release with 'Default' analysis mode. Rules that are first released in a version later than '9.0' are disabled.

is_global = true

global_level = -100


# CA1873: Avoid potentially expensive logging
dotnet_diagnostic.CA1873.severity = none

# CA1874: Use 'Regex.IsMatch'
dotnet_diagnostic.CA1874.severity = none

# CA1875: Use 'Regex.Count'
dotnet_diagnostic.CA1875.severity = none

# CA2023: Invalid braces in message template
dotnet_diagnostic.CA2023.severity = none

# CA2024: Do not use 'StreamReader.EndOfStream' in async methods
dotnet_diagnostic.CA2024.severity = none

# CA2025: Do not pass 'IDisposable' instances into unawaited tasks
dotnet_diagnostic.CA2025.severity = none
