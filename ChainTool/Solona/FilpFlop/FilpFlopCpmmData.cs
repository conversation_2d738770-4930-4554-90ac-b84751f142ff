using System.IO;
using Solnet.Wallet;

namespace ChainTool;

public class FilpFlopCpmmData {
   
    // 公钥字段
    public PublicKey amm_config { get; set; }
    public PublicKey pool_creator { get; set; }
    public PublicKey token_0_vault { get; set; }//wsol vault
    public PublicKey token_1_vault { get; set; }//mint vault
    public PublicKey lp_mint { get; set; }
    public PublicKey token_0_mint { get; set; }
    public PublicKey token_1_mint { get; set; }
    public PublicKey token_0_program { get; set; }
    public PublicKey token_1_program { get; set; }
    public PublicKey observation_key { get; set; }
    
    // 基本类型字段
    public byte auth_bump { get; set; }
    public byte status { get; set; }
    public byte lp_mint_decimals { get; set; }
    public byte mint_0_decimals { get; set; }
    public byte mint_1_decimals { get; set; }
    
    // 数值字段
    public ulong lp_supply { get; set; }
    public ulong protocol_fees_token_0 { get; set; }
    public ulong protocol_fees_token_1 { get; set; }
    public ulong fund_fees_token_0 { get; set; }
    public ulong fund_fees_token_1 { get; set; }
    public ulong open_time { get; set; }
    public ulong recent_epoch { get; set; }
    
    // 数组字段
    public ulong[] padding { get; set; } = new ulong[31];
    
    public PublicKey pool;
    public ulong solAmount;
    public ulong tokenAmount;
    
    // 二进制解析方法
    public static FilpFlopCpmmData Decode(byte[] data)
    {
        if (data==null||data.Length<637) {
            return null;
        }
        var cpmmData = new FilpFlopCpmmData();
        using MemoryStream ms = new MemoryStream(data);
        using BinaryReader br = new BinaryReader(ms);
        
        // 读取公钥字段
        br.ReadBytes(8);//跳过前面
        
        cpmmData.amm_config = new PublicKey(br.ReadBytes(32));
        
        cpmmData.pool_creator = new PublicKey(br.ReadBytes(32));
        
        cpmmData.token_0_vault = new PublicKey(br.ReadBytes(32));
        
        cpmmData.token_1_vault = new PublicKey(br.ReadBytes(32));
        
        cpmmData.lp_mint = new PublicKey(br.ReadBytes(32));
        
        cpmmData.token_0_mint = new PublicKey(br.ReadBytes(32));
        
        cpmmData.token_1_mint = new PublicKey(br.ReadBytes(32));
        
        cpmmData.token_0_program = new PublicKey(br.ReadBytes(32));
        
        cpmmData.token_1_program = new PublicKey(br.ReadBytes(32));
        
        cpmmData.observation_key = new PublicKey(br.ReadBytes(32));
        
        // 读取基本类型字段
        cpmmData.auth_bump = br.ReadByte();
        cpmmData.status = br.ReadByte();
        cpmmData.lp_mint_decimals = br.ReadByte();
        cpmmData.mint_0_decimals = br.ReadByte();
        cpmmData.mint_1_decimals = br.ReadByte();
        
        // 读取数值字段
        cpmmData.lp_supply = br.ReadUInt64();
        cpmmData.protocol_fees_token_0 = br.ReadUInt64();
        cpmmData.protocol_fees_token_1 = br.ReadUInt64();
        cpmmData.fund_fees_token_0 = br.ReadUInt64();
        cpmmData.fund_fees_token_1 = br.ReadUInt64();
        cpmmData.open_time = br.ReadUInt64();
        cpmmData.recent_epoch = br.ReadUInt64();
        
        // 读取padding数组
        cpmmData.padding = new ulong[31];
        for (int i = 0; i < 31; i++)
        {
            cpmmData.padding[i] = br.ReadUInt64();
        }
        
        return cpmmData;
    }

    public double price() {
        return ((double)solAmount/(double)tokenAmount);
    }
}
