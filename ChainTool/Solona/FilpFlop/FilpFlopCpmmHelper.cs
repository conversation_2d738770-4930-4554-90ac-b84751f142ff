using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Shared;
using Solnet.Programs;
using Solnet.Programs.PumpFunAmm;
using Solnet.Wallet;

namespace ChainTool;

public class FilpFlopCpmmHelper {

    public static readonly PublicKey ProgramId = new PublicKey("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");
    
    public static PublicKey GetUserQuoteATA(PublicKey owner, PublicKey mint) {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { owner.KeyBytes, TokenProgram.ProgramIdKey.KeyBytes, mint.KeyBytes, },
            AssociatedTokenAccountProgram.ProgramIdKey, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    private const string CPMM_POOL_SEED = "pool";
    /// <summary>
    /// Event authority PDA seed
    /// </summary>
    private const string EventAuthoritySeed = "__event_authority";

    /// <summary>
    /// Instruction discriminators based on IDL
    /// </summary>
    public static class Instructions {
        public static readonly byte[] swap_base_input = { 143, 190, 90, 218, 196, 30, 51, 222 };
        // public static readonly byte[] swap_base_output = { 55, 217, 98, 86, 163, 74, 180, 173 };
        
    }


    public static void Test() {
        //BXifvnaiJPjhgNYDrcvvN8qK4aXniTtVtNpfzpodwHAG
        string mint = "xoTvq1X9XFRthKQTSawpwsAxjNUpgZqgD4XfLTMbonk";
        var amm_config = "D4FPEruKEHrG5TenZ2mpDGEfu1iUvTiqBxvpU8HLBvC2";

        for (ushort i = 0; i < 10; i++) {
             var amm_config1 = GetAmmConfigPDA(i);
             Console.WriteLine($"amm`: {amm_config1}");
        }
        // BXifvnaiJPjhgNYDrcvvN8qK4aXniTtVtNpfzpodwHAG
        var pool = GetCPMMPda(new PublicKey(mint), new PublicKey(amm_config));
        Console.WriteLine($"pool: {pool}");
    }
    
    public static PublicKey GetCPMMPda(PublicKey mint,PublicKey amm_config) {
        
        // var amm_config = GetAmmConfigPDA(0);
        
        
        
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { 
                Encoding.UTF8.GetBytes(CPMM_POOL_SEED), 
                amm_config.KeyBytes,
                new PublicKey(Config.TOKEN_WSOL).KeyBytes ,
                mint.KeyBytes, 
            },
            
            ProgramId,
            out PublicKey outAddress, out byte bump);
        return outAddress;
    }
    
    
    public static PublicKey GetAmmConfigPDA(ushort index) {
        // 将ushort转换为大端序的字节数组
        byte[] indexBytes = BitConverter.GetBytes(index);
        if (BitConverter.IsLittleEndian)
        {
            // 如果系统是小端序，则需要反转字节顺序
            Array.Reverse(indexBytes);
        }
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { 
                Encoding.UTF8.GetBytes("amm_config"), 
                indexBytes 
            },
            ProgramId, out PublicKey outAddress, out byte bump);
        return outAddress;
    }


    /// <summary>
    /// Calculate the event authority PDA
    /// </summary>
    public static PublicKey GetEventAuthorityPda() {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { Encoding.UTF8.GetBytes(EventAuthoritySeed) },
            ProgramId, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    

    public static FilpFlopCpmmData DecodeBondingCurveData(byte[] data) =>FilpFlopCpmmData.Decode(data);

    public static async Task<FilpFlopCpmmData> GetPoolData(string mint,string ammConfig) {
        // BXifvnaiJPjhgNYDrcvvN8qK4aXniTtVtNpfzpodwHAG
        var poolPDA = GetCPMMPda(new PublicKey(mint), new PublicKey(ammConfig));
        Log.d($"{mint}, cpmm pool PDA: {poolPDA}");
        
        FilpFlopCpmmData bondingCurveData = new FilpFlopCpmmData();
        var accountInfo = await Config.rpcClient.GetAccountInfoAsync(poolPDA);

        if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null) {
            Log.w($"无法获取cpmm数据: {accountInfo.Reason}");
            bondingCurveData.pool = poolPDA;
            bondingCurveData.solAmount = 0;
            bondingCurveData.tokenAmount = 0;
            return bondingCurveData;
        }

        var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
        bondingCurveData = FilpFlopCpmmData.Decode(data);

        bondingCurveData.solAmount = await SolUtil.GetTokenAccountBalance(bondingCurveData.token_0_vault);
        bondingCurveData.tokenAmount = await SolUtil.GetTokenAccountBalance(bondingCurveData.token_1_vault);
        bondingCurveData.pool = poolPDA;
        return bondingCurveData;
    }
    public static async Task<FilpFlopCpmmData> GetPoolData(string pool) {
        // BXifvnaiJPjhgNYDrcvvN8qK4aXniTtVtNpfzpodwHAG
        var accountInfo = await Config.rpcClient.GetAccountInfoAsync(pool);
        FilpFlopCpmmData bondingCurveData = new FilpFlopCpmmData();
        if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null) {
            Log.w($"无法获取cpmm数据: {accountInfo.Reason}");
            bondingCurveData.pool = new PublicKey(pool);
            bondingCurveData.solAmount = 0;
            bondingCurveData.tokenAmount = 0;
            return bondingCurveData;
        }

        var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
         bondingCurveData = FilpFlopCpmmData.Decode(data);
        
        bondingCurveData.solAmount = await SolUtil.GetTokenAccountBalance(bondingCurveData.token_0_vault);
        bondingCurveData.tokenAmount = await SolUtil.GetTokenAccountBalance(bondingCurveData.token_1_vault);
        bondingCurveData.pool = new PublicKey(pool);
        return bondingCurveData;
    }
}
