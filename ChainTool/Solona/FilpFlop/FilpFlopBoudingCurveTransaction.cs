using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using ChainTool.Models;
using Solnet.Programs;
using Solnet.Rpc.Builders;
using Solnet.Rpc.Models;
using Solnet.Wallet;
using Log = Shared.Log;

namespace ChainTool;

public class FilpFlopBoudingCurveTransaction : ITokenTransaction {

    public async Task<bool> buy(string walletPublicKey
      , string walletPrivateKey
      , string mint
      , ulong tokenAmount
      , double tokenPrice
      , double slippagePercent = 5
      , double fee = 0
    ) {
        try {
            if (string.IsNullOrEmpty(walletPublicKey) ||
                string.IsNullOrEmpty(walletPrivateKey) ||
                string.IsNullOrEmpty(mint) ||
                tokenPrice < 0 ||
                slippagePercent < 0 ||
                fee < 0) {
                Log.w($"buy fail,参数错误,{walletPublicKey},{walletPrivateKey},{mint},{tokenPrice},{slippagePercent},{fee}");
                return false;
            }

            tokenAmount = tokenAmount * 1_000_000;

            var bondingCurve = BonkBoundingCurveHelper.GetBondingCurvePda(new PublicKey(mint));
            // 6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX
            var config = BonkBoundingCurveHelper.GetGlobalPda(0, 0);

            //FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1

            var accountInfo = await Config.rpcClient.GetAccountInfoAsync(bondingCurve);

            if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null) {
                Console.WriteLine($"无法获取bonding curve数据: {accountInfo.Reason}");
                return false;
            }

            var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
            var bondingCurveData = BonkBoundingCurveHelper.DecodeBondingCurveData(data);

            var wallet = new PublicKey(walletPublicKey);

            //内池交易参数,15个
            var payer = walletPublicKey; //1
            var authority = new PublicKey("WLHv2UAZm6z4KyaaELi5pjdbJh6RESMva1Rnn8pJVVh");
            var globalConfig = bondingCurveData.global_config;
            var platformConfig = bondingCurveData.platform_config;
            var poolState = bondingCurve;

            var UserTokenPDA = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(wallet, new PublicKey(mint));
            var UserWSolnPDA = BonkBoundingCurveHelper.GetUserQuoteATA(wallet, new PublicKey(Config.TOKEN_WSOL));

            var base_vault = bondingCurveData.base_vault; //8
            var quote_vault = bondingCurveData.quote_vault;
            var tokenMint = new PublicKey(mint);
            var tokenWSol = new PublicKey(Config.TOKEN_WSOL); //11

            var baseTokenProgram = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
            var baseWSOLProgram = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");

            var eventAuthority = BonkBoundingCurveHelper.GetEventAuthorityPda();
            var program = BonkBoundingCurveHelper.ProgramId; //15

            tokenPrice = bondingCurveData.price();

            PublicKey baseMintKey = tokenMint;
            PublicKey quoteMintKey = tokenWSol;
            PublicKey pool = poolState;
            PublicKey poolBaseTokenAccount = base_vault;
            PublicKey poolQuoteTokenAccount = quote_vault;

            // 基础SOL成本（1 SOL = 1,000,000,000 lamports）
            double baseSolCost = tokenPrice * tokenAmount;

            Log.d($"买入:{tokenAmount}代币, SOL成本: {baseSolCost / 1_000_000_000.0:F9} SOL");

            // 计算包含滑点的最大SOL成本
            double maxSolCost = SolUtil.CalculateMaxSolCostWithSlippage(baseSolCost, slippagePercent);
            // double maxSolCost = 1000000;
            Log.d($"最大SOL成本(含{slippagePercent}%滑点): {maxSolCost / 1_000_000_000.0:F9} SOL");

            // 设置最大交易计算单元
            uint units = 200_000;
            TransactionInstruction computeUnitLimitInstruction = ComputeBudgetHelper.SetComputeUnitLimit(units);

            // 创建优先交易费的指令
            double d = SolUtil.SolToLamp(fee);
            ulong priorityRate = (ulong)(d / units);
            TransactionInstruction priorityFeeInstruction = ComputeBudgetHelper.SetComputeUnitPrice(priorityRate);

            // 初始化钱包和客户端
            var account = WalletHelper.CreateAccountFromKeyPair(walletPrivateKey, walletPublicKey);
            // 获取用户代币账户 ,  72jyfCgTKZ7PbtkP1SHRyYDSauZohasUhBZhxYFwwyoZ
            PublicKey userBaseTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, baseMintKey);

            // 获取用户WSOL账户//AzcY5dwHdknr4LnWJPbzQ3xWQrWYaqikskVxD8X5xsYD
            PublicKey userQuoteTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, quoteMintKey);

            // 构建交易
            var lastBlockRsp = await Config.rpcClient.GetLatestBlockHashAsync();

            if (lastBlockRsp.WasSuccessful == false) {
                Log.w($"get last block hash failed");
                return false;
            }

            string recentBlockHash = lastBlockRsp.Result.Value.Blockhash;

            var txBuilder = new TransactionBuilder()
               .SetRecentBlockHash(recentBlockHash)
               .SetFeePayer(account)
               .AddInstruction(computeUnitLimitInstruction)
               .AddInstruction(priorityFeeInstruction);

            // 检查用户代币账户是否存在，如果不存在则创建
            var baseAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userBaseTokenAccount);

            if (!baseAccountInfo.WasSuccessful || baseAccountInfo.Result?.Value == null) {
                TransactionInstruction createBaseAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, baseMintKey);
                txBuilder.AddInstruction(createBaseAtaInstruction);
            }

            var quoteAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userQuoteTokenAccount);

            if (!quoteAccountInfo.WasSuccessful || quoteAccountInfo.Result?.Value == null) {
                TransactionInstruction createQuoteAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, quoteMintKey);
                txBuilder.AddInstruction(createQuoteAtaInstruction);
            }

            // 创建买入指令
            var instructionData = new List<byte>();
            instructionData.AddRange(BonkBoundingCurveHelper.Instructions.buyExactIn);

            ulong amount_B = (ulong)maxSolCost;
            instructionData.AddRange(BitConverter.GetBytes(amount_B)); // cost max  sol
            ulong minimum_amount_A = tokenAmount;
            // ulong minimum_amount_A = ***********;
            instructionData.AddRange(BitConverter.GetBytes(minimum_amount_A)); // minimum_amount_out
            ulong share_fee_rate = 0;
            instructionData.AddRange(BitConverter.GetBytes(share_fee_rate)); // share_fee_rate

            // 账户列表，按照IDL中的顺序
            var keys = new List<AccountMeta> {
                AccountMeta.Writable(account.PublicKey, true)
              , // user
                AccountMeta.ReadOnly(authority, false)
              , AccountMeta.ReadOnly(globalConfig, false)
              , AccountMeta.ReadOnly(platformConfig, false)
              , AccountMeta.Writable(pool, false)
              , AccountMeta.Writable(userBaseTokenAccount, false)
              , // user
                AccountMeta.Writable(userQuoteTokenAccount, false)
              , // user

                AccountMeta.Writable(base_vault, false)
              , AccountMeta.Writable(quote_vault, false)
              , AccountMeta.ReadOnly(baseMintKey, false)
              , AccountMeta.ReadOnly(quoteMintKey, false)
              , AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)
              , //12 base_token_program
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)
              , //13 quote_token_program
                AccountMeta.ReadOnly(eventAuthority, false)
              , //14

                AccountMeta.ReadOnly(BonkBoundingCurveHelper.ProgramId, false)
              , //15
            };

            TransactionInstruction swapTranaction = new TransactionInstruction {
                Keys = keys, ProgramId = BonkBoundingCurveHelper.ProgramId, Data = instructionData.ToArray()
            };

            TransactionInstruction transferSolInstruction = SystemProgram.Transfer(
                account.PublicKey,
                userQuoteTokenAccount,
                (ulong)maxSolCost);

            TransactionInstruction syncNativeInstruction = new TransactionInstruction {
                ProgramId = TokenProgram.ProgramIdKey
              , Keys = new List<AccountMeta> { AccountMeta.Writable(userQuoteTokenAccount, false) }
              , Data = new byte[] { 17 } // SyncNative instruction
            };
            // 关闭账户指令
            TransactionInstruction closeAccountInstruction = SolUtil.buildCloseAccountInstruction(userQuoteTokenAccount, account);

            txBuilder.AddInstruction(transferSolInstruction);
            txBuilder.AddInstruction(syncNativeInstruction);
            txBuilder.AddInstruction(swapTranaction);
            txBuilder.AddInstruction(closeAccountInstruction);

            // 构建并发送交易
            var tx = txBuilder.Build(account);
            var result = await Config.rpcClient.SendTransactionAsync(tx);

            if (result.WasSuccessful) {
                Log.d($"买入交易成功，交易哈希: {result.Result}");

                TransactionHistory.saveToDb(
                    walletPublicKey
                  , Config.TOKEN_SOL
                  , tokenMint
                  , solAmount: "-" + (baseSolCost / 1_000_000_000D)
                  , mintAmout: tokenAmount.ToString(), txHash: result.Result);
                return true;
            } else {
                Log.d($"买入交易失败: {result.Reason}");
                return false;
            }
        } catch (Exception ex) {
            Log.w($"买入执行失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> sell(string walletPublicKey
      , string privateKey
      , string mint
      , ulong tokenAmount
      , double tokenPrice
      , double slippagePercent = 5
      , double fee = 0
    ) {
        try {
            if (string.IsNullOrEmpty(walletPublicKey) ||
                string.IsNullOrEmpty(privateKey) ||
                string.IsNullOrEmpty(mint) ||
                tokenPrice < 0 ||
                slippagePercent < 0 ||
                fee < 0) {
                Log.w($"buy fail,参数错误,{walletPublicKey},{privateKey},{mint},{tokenPrice},{slippagePercent},{fee}");
                return false;
            }

            tokenAmount = tokenAmount * 1_000_000;

            var bondingCurve = BonkBoundingCurveHelper.GetBondingCurvePda(new PublicKey(mint));
            // 6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX
            var config = BonkBoundingCurveHelper.GetGlobalPda(0, 0);

            //FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1

            var accountInfo = await Config.rpcClient.GetAccountInfoAsync(bondingCurve);

            if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null) {
                Console.WriteLine($"无法获取bonding curve数据: {accountInfo.Reason}");
                return false;
            }

            var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
            var bondingCurveData = BonkBoundingCurveHelper.DecodeBondingCurveData(data);

            var wallet = new PublicKey(walletPublicKey);

            //内池交易参数,15个
            var payer = walletPublicKey; //1
            var authority = new PublicKey("WLHv2UAZm6z4KyaaELi5pjdbJh6RESMva1Rnn8pJVVh");
            var globalConfig = bondingCurveData.global_config;
            var platformConfig = bondingCurveData.platform_config;
            var poolState = bondingCurve;

            var UserTokenPDA = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(wallet, new PublicKey(mint));
            var UserWSolnPDA = BonkBoundingCurveHelper.GetUserQuoteATA(wallet, new PublicKey(Config.TOKEN_WSOL));

            var base_vault = bondingCurveData.base_vault; //8
            var quote_vault = bondingCurveData.quote_vault;
            var tokenMint = new PublicKey(mint);
            var tokenWSol = new PublicKey(Config.TOKEN_WSOL); //11

            var baseTokenProgram = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
            var baseWSOLProgram = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");

            var eventAuthority = BonkBoundingCurveHelper.GetEventAuthorityPda();
            var program = BonkBoundingCurveHelper.ProgramId; //15

            tokenPrice = bondingCurveData.price();

            PublicKey baseMintKey = tokenMint;
            PublicKey quoteMintKey = tokenWSol;
            PublicKey pool = poolState;
            PublicKey poolBaseTokenAccount = base_vault;
            PublicKey poolQuoteTokenAccount = quote_vault;

            // 基础SOL成本（1 SOL = 1,000,000,000 lamports）
            double baseSolCost = tokenPrice * tokenAmount;

            Log.d($"买入:{tokenAmount}代币, SOL成本: {baseSolCost / 1_000_000_000.0:F9} SOL");

            // 计算包含滑点的最大SOL成本
            double maxSolCost = SolUtil.CalculateMinSolCostWithSlippage(baseSolCost, slippagePercent);
            // double maxSolCost = 1000000;

            Log.d($"最大SOL成本(含{slippagePercent}%滑点): {maxSolCost / 1_000_000_000.0:F9} SOL");

            // 设置最大交易计算单元
            uint units = 200_000;
            TransactionInstruction computeUnitLimitInstruction = ComputeBudgetHelper.SetComputeUnitLimit(units);

            // 创建优先交易费的指令
            double d = SolUtil.SolToLamp(fee);
            ulong priorityRate = (ulong)(d / units);
            TransactionInstruction priorityFeeInstruction = ComputeBudgetHelper.SetComputeUnitPrice(priorityRate);

            // 初始化钱包和客户端
            var account = WalletHelper.CreateAccountFromKeyPair(privateKey, walletPublicKey);
            // 获取用户代币账户 ,  72jyfCgTKZ7PbtkP1SHRyYDSauZohasUhBZhxYFwwyoZ
            PublicKey userBaseTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, baseMintKey);

            // 获取用户WSOL账户//AzcY5dwHdknr4LnWJPbzQ3xWQrWYaqikskVxD8X5xsYD
            PublicKey userQuoteTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, quoteMintKey);

            // 构建交易
            var lastBlockRsp = await Config.rpcClient.GetLatestBlockHashAsync();

            if (lastBlockRsp.WasSuccessful == false) {
                Log.w($"get last block hash failed");
                return false;
            }

            string recentBlockHash = lastBlockRsp.Result.Value.Blockhash;

            var txBuilder = new TransactionBuilder()
               .SetRecentBlockHash(recentBlockHash)
               .SetFeePayer(account)
               .AddInstruction(computeUnitLimitInstruction)
               .AddInstruction(priorityFeeInstruction);

            // 检查用户代币账户是否存在，如果不存在则创建
            var baseAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userBaseTokenAccount);

            if (!baseAccountInfo.WasSuccessful || baseAccountInfo.Result?.Value == null) {
                TransactionInstruction createBaseAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, baseMintKey);
                txBuilder.AddInstruction(createBaseAtaInstruction);
            }

            var quoteAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userQuoteTokenAccount);

            if (!quoteAccountInfo.WasSuccessful || quoteAccountInfo.Result?.Value == null) {
                TransactionInstruction createQuoteAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, quoteMintKey);
                txBuilder.AddInstruction(createQuoteAtaInstruction);
            }

            // 创建买入指令
            var instructionData = new List<byte>();
            instructionData.AddRange(BonkBoundingCurveHelper.Instructions.sellExactIn);

            ulong minimum_amount_A = tokenAmount;
            // ulong minimum_amount_A = ***********;
            instructionData.AddRange(BitConverter.GetBytes(minimum_amount_A)); // minimum_amount_out

            ulong amount_B = (ulong)maxSolCost;
            instructionData.AddRange(BitConverter.GetBytes(amount_B)); // cost max  sol

            ulong share_fee_rate = 0;
            instructionData.AddRange(BitConverter.GetBytes(share_fee_rate)); // share_fee_rate

            // 账户列表，按照IDL中的顺序
            var keys = new List<AccountMeta> {
                AccountMeta.Writable(account.PublicKey, true)
              , // user
                AccountMeta.ReadOnly(authority, false)
              , AccountMeta.ReadOnly(globalConfig, false)
              , AccountMeta.ReadOnly(platformConfig, false)
              , AccountMeta.Writable(pool, false)
              , AccountMeta.Writable(userBaseTokenAccount, false)
              , // user
                AccountMeta.Writable(userQuoteTokenAccount, false)
              , // user

                AccountMeta.Writable(base_vault, false)
              , AccountMeta.Writable(quote_vault, false)
              , AccountMeta.ReadOnly(baseMintKey, false)
              , AccountMeta.ReadOnly(quoteMintKey, false)
              , AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)
              , //12 base_token_program
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)
              , //13 quote_token_program
                AccountMeta.ReadOnly(eventAuthority, false)
              , //14

                AccountMeta.ReadOnly(BonkBoundingCurveHelper.ProgramId, false)
              , //15
            };

            TransactionInstruction swapTransation = new TransactionInstruction {
                Keys = keys, ProgramId = BonkBoundingCurveHelper.ProgramId, Data = instructionData.ToArray()
            };

            // 关闭账户指令
            TransactionInstruction closeAccountInstruction = SolUtil.buildCloseAccountInstruction(userQuoteTokenAccount, account);

            txBuilder.AddInstruction(swapTransation);
            txBuilder.AddInstruction(closeAccountInstruction);

            // 构建并发送交易
            var tx = txBuilder.Build(account);
            var result = await Config.rpcClient.SendTransactionAsync(tx);

            if (result.WasSuccessful) {
                Log.d($"买入交易成功，交易哈希: {result.Result}");

                TransactionHistory.saveToDb(
                    walletPublicKey
                  , Config.TOKEN_SOL
                  , tokenMint
                  , solAmount: (baseSolCost / 1_000_000_000D).ToString()
                  , mintAmout: "-" + tokenAmount.ToString(), txHash: result.Result);
                return true;
            } else {
                Log.d($"买入交易失败: {result.Reason}");
                return false;
            }
        } catch (Exception ex) {
            Log.w($"买入执行失败: {ex.Message}");
            return false;
        }
    }

}
