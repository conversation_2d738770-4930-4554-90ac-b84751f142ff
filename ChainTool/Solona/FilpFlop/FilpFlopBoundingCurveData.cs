using System;
using System.IO;
using Solnet.Wallet;

namespace ChainTool;

public class FilpFlopBoundingCurveData {
    public ulong epoch;
    public byte  auth_bump;
    public byte  status;
    public byte  base_decimals;
    public byte  quote_decimals;
    public byte  migrate_type;
    public ulong supply;
    public ulong total_base_sell;
    public ulong virtual_base;
    public ulong virtual_quote;
    public ulong real_base;
    public ulong real_quote;
    public ulong total_quote_fund_raising;
    public ulong quote_protocol_fee;
    public ulong platform_fee;
    public ulong migrate_fee;
    
    public VestingSchedule vesting_schedule;
    
    public PublicKey global_config;
    public PublicKey platform_config;
    public PublicKey base_mint;
    public PublicKey quote_mint;
    public ulong tokenAmount ;
    public ulong solAmount ;
    public PublicKey base_vault;
    public PublicKey quote_vault;
    public PublicKey creator;
    public ulong[] padding = new ulong[8];

    public double price() => (double)(virtual_quote+real_quote)/(double)(virtual_base-real_base);
    
    public class VestingSchedule {
        public ulong total_locked_amount;
        public ulong cliff_period;
        public ulong unlock_period;
        public ulong start_time;
        public ulong allocated_share_amount;
    }
    public static BonkBoundingCurveData Decode(byte[] data) {
        if (data.Length < 41) // 最小数据长度检查
        {
            throw new ArgumentException("Bonding curve数据长度不足");
        }

        var bondingCurve = new BonkBoundingCurveData();
        using MemoryStream ms = new MemoryStream(data);
        using BinaryReader br = new BinaryReader(ms);

        //跳过前8个字节
        br.ReadUInt64();

        // 基本字段
        bondingCurve.epoch = br.ReadUInt64();
        bondingCurve.auth_bump = br.ReadByte();
        bondingCurve.status = br.ReadByte();
        bondingCurve.base_decimals = br.ReadByte();
        bondingCurve.quote_decimals = br.ReadByte();
        bondingCurve.migrate_type = br.ReadByte();

        // 数值字段
        bondingCurve.supply = br.ReadUInt64();
        bondingCurve.total_base_sell = br.ReadUInt64();
        bondingCurve.virtual_base = br.ReadUInt64();
        bondingCurve.virtual_quote = br.ReadUInt64();
        bondingCurve.real_base = br.ReadUInt64();
        bondingCurve.real_quote = br.ReadUInt64();
        bondingCurve.total_quote_fund_raising = br.ReadUInt64();
        bondingCurve.quote_protocol_fee = br.ReadUInt64();
        bondingCurve.platform_fee = br.ReadUInt64();
        bondingCurve.migrate_fee = br.ReadUInt64();

        // 解析 vesting_schedule 数组
        // 首先读取数组长度（假设是一个字节表示长度）
        var schedule = new BonkBoundingCurveData.VestingSchedule {
            total_locked_amount = br.ReadUInt64()
          , cliff_period = br.ReadUInt64()
          , unlock_period = br.ReadUInt64()
          , start_time = br.ReadUInt64()
          , allocated_share_amount = br.ReadUInt64()
        };

        // 读取 PublicKey 字段
        byte[] keyBytes = new byte[32]; // PublicKey 是 32 字节

        br.Read(keyBytes, 0, 32);
        bondingCurve.global_config = new PublicKey(keyBytes);

        br.Read(keyBytes, 0, 32);
        bondingCurve.platform_config = new PublicKey(keyBytes);

        br.Read(keyBytes, 0, 32);
        bondingCurve.base_mint = new PublicKey(keyBytes);

        br.Read(keyBytes, 0, 32);
        bondingCurve.quote_mint = new PublicKey(keyBytes);

        br.Read(keyBytes, 0, 32);
        bondingCurve.base_vault = new PublicKey(keyBytes);

        br.Read(keyBytes, 0, 32);
        bondingCurve.quote_vault = new PublicKey(keyBytes);

        br.Read(keyBytes, 0, 32);
        bondingCurve.creator = new PublicKey(keyBytes);

        // 读取 padding
        bondingCurve.padding = new ulong[8];

        for (int i = 0; i < 8; i++) { bondingCurve.padding[i] = br.ReadUInt64(); }

        return bondingCurve;
    }
}
