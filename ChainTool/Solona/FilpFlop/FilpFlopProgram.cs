using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ChainTool.Models;
using Solnet.Programs;
using Solnet.Rpc.Builders;
using Solnet.Rpc.Models;
using Solnet.Wallet;
using Log = Shared.Log;
namespace ChainTool
{
    //https://github.com/rckprtr/pumpdotfun-sdk/blob/3507b88e7c6371ec42cc111226b6e07bc599b867/src/pumpfun.ts#L75
    
    /// <summary>
    /// Pump.fun Program implementation based on IDL
    /// Program ID: 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
    /// </summary>
    public static class FilpFlopProgram {
        public const string AmmPool = "dqtN2z52z7ruTBnX2acwukDQJRTStCrVCUitD7nKVy6";

        public const string AmmConfig = "D4FPEruKEHrG5TenZ2mpDGEfu1iUvTiqBxvpU8HLBvC2";
        
        public static Dictionary<string, FilpFlopCpmmData> cachedPoolData = new Dictionary<string, FilpFlopCpmmData>();

        // raydium
        public static readonly PublicKey FARM_PROGRAM_ID_V3 = new PublicKey("EhhTKczWMGQt46ynNeRX1WfeagwwJd7ufHvCDjRxjo5Q");
        // temp fusion
        public static readonly PublicKey FARM_PROGRAM_ID_V4 = new PublicKey("CBuCnLe26faBpcBP2fktp4rp8abpcAnTWft6ZrP5Q4T");
        // "fusion"
        public static readonly PublicKey FARM_PROGRAM_ID_V5 = new PublicKey("9KEPoZmtHUrBbhWN1v1KWLMkkvwY6WLtAVUCPRtRjP4z");
        // echosystem
        public static readonly PublicKey FARM_PROGRAM_ID_V6 = new PublicKey("FarmqiPv5eAj3j1GMdMCMUGXqPUvmquZtMy86QH6rzhG");

        public static readonly PublicKey UTIL1216 = new PublicKey("CLaimxFqjHzgTJtAGHU47NPhg6qrc5sCnpC4tBLyABQS");

        public static readonly PublicKey OPEN_BOOK_PROGRAM = new PublicKey("srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX");
        public static readonly PublicKey SERUM_PROGRAM_ID_V3 = new PublicKey("9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin");

        public static readonly PublicKey AMM_V4 = new PublicKey("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8");
        public static readonly PublicKey AMM_STABLE = new PublicKey("5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h");
        public static readonly PublicKey LIQUIDITY_POOL_PROGRAM_ID_V5_MODEL = new PublicKey("CDSr3ssLcRB6XYPJwAfFt18MZvEZp4LjHcvzBVZ45duo");
        public static readonly PublicKey CLMM_PROGRAM_ID = new PublicKey("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK");
        public static readonly PublicKey CLMM_LOCK_PROGRAM_ID = new PublicKey("LockrWmn6K5twhz3y9w1dQERbmgSaRkfnTeTKbpofwE");
        public static readonly PublicKey CLMM_LOCK_AUTH_ID = new PublicKey("kN1kEznaF5Xbd8LYuqtEFcxzWSBk5Fv6ygX6SqEGJVy");

        public static readonly PublicKey Router = new PublicKey("routeUGWgWzqBWFcrCfv8tritsqukccJPu3q5GPP3xS");
        public static readonly PublicKey FEE_DESTINATION_ID = new PublicKey("7YttLkHDoNj9wyDur5pM1ejNaAvT9X4eqaYcHQqtj2G5");

        public static readonly PublicKey IDO_PROGRAM_ID_V1 = new PublicKey("6FJon3QE27qgPVggARueB22hLvoh22VzJpXv4rBEoSLF");
        public static readonly PublicKey IDO_PROGRAM_ID_V2 = new PublicKey("CC12se5To1CdEuw7fDS27B7Geo5jJyL7t5UK2B44NgiH");
        public static readonly PublicKey IDO_PROGRAM_ID_V3 = new PublicKey("9HzJyW1qZsEiSfMUf6L2jo3CcTKAyBmSyKdwQeYisHrC");
        public static readonly PublicKey IDO_PROGRAM_ID_V4 = new PublicKey("DropEU8AvevN3UrXWXTMuz3rqnMczQVNjq3kcSdW2SQi");

        public static readonly PublicKey CREATE_CPMM_POOL_PROGRAM = new PublicKey("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C");
        public static readonly PublicKey CREATE_CPMM_POOL_AUTH = new PublicKey("GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL");
        public static readonly PublicKey CREATE_CPMM_POOL_FEE_ACC = new PublicKey("DNXgeM9EiiaAbaWvwjHj9fQQLAX5ZsfHyvmYUNRAdNC8");

        public static readonly PublicKey DEV_CREATE_CPMM_POOL_PROGRAM = new PublicKey("CPMDWBwJDtYax9qW7AyRuVC19Cc4L4Vcy4n2BHAbHkCW");
        public static readonly PublicKey DEV_CREATE_CPMM_POOL_AUTH = new PublicKey("7rQ1QFNosMkUCuh7Z7fPbTHvh73b68sQYdirycEzJVuw");
        public static readonly PublicKey DEV_CREATE_CPMM_POOL_FEE_ACC = new PublicKey("G11FKBRaAkHAKuLCgLM6K6NUc9rTjPAznRCjZifrTQe2");

        public static readonly PublicKey LOCK_CPMM_PROGRAM = new PublicKey("LockrWmn6K5twhz3y9w1dQERbmgSaRkfnTeTKbpofwE");
        public static readonly PublicKey DEV_LOCK_CPMM_PROGRAM = new PublicKey("DLockwT7X7sxtLmGH9g5kmfcjaBtncdbUmi738m5bvQC");

        public static readonly PublicKey LOCK_CPMM_AUTH = new PublicKey("3f7GcQFG397GAaEnv51zR6tsTVihYRydnydDD1cXekxH");
        public static readonly PublicKey DEV_LOCK_CPMM_AUTH = new PublicKey("7AFUeLVRjBfzqK3tTGw8hN48KLQWSk6DTE8xprWdPqix");

        public static readonly PublicKey LAUNCHPAD_AUTH = new PublicKey("WLHv2UAZm6z4KyaaELi5pjdbJh6RESMva1Rnn8pJVVh");

        public static readonly PublicKey DEV_LAUNCHPAD_PROGRAM = new PublicKey("LanD8FpTBBvzZFXjTxsAoipkFsxPUCDB4qAqKxYDiNP");
        public static readonly PublicKey DEV_LAUNCHPAD_AUTH = new PublicKey("HYNHiyKJ3gGVFvyxJAurK7qr7P2o5J9THmvCGMdULtpW");

        public static async Task<AmmMarketPair> GetMarketPair(string token,string ammConfig = AmmConfig,string pool = AmmPool) {


            List<string> amm_configs = new List<string>();

            
            //比较solana的余额, 选择最多的那个作为交易的Pool
            FilpFlopCpmmData ammPoolData = await FilpFlopCpmmHelper.GetPoolData(token, ammConfig);
            cachedPoolData[token] = ammPoolData;

            var ammMarket = new AmmMarket() {
                pool = ammPoolData.pool
              , programID = FilpFlopProgram.CREATE_CPMM_POOL_PROGRAM.Key
              , programName = "FlipFlopAmm"
              , mint = token
              , tokenAmount = ammPoolData.tokenAmount.ToString()
              , solAmount = ammPoolData.solAmount.ToString()
              , price = ammPoolData.price().ToString(CultureInfo.InvariantCulture)
               ,
            };

            var market = new AmmMarketPair() {
                innerMarket = null, 
                ammMarket = ammMarket,
                mint = token,
            };

            return market;
        }



        public static  void Test() {
            _= TestAsync();
            Console.ReadLine();
        }

        public static async Task<bool> TestAsync() {
            var walletPrivateKey = "3CEofxZsQZibhUq54LCGBUMwMFwKRD6SKJL2tNVbLqU1BZFPXkeDegYA9YWK7aQrkE5My7o5nvxzLDXnJJFRveuD";
            var walletPublicKey = new PublicKey("9Tkq9jeo5mg6pXmR18qFT2hYuxE2aMaycLfSGKbH6hp3");
            // 5SY3rKqwQnuWWcTiEGd1r7EEiJSquitjuPSwWFFgNpVv
            string mint = "Awsri7DNcd98WQSuuNwJaRoTn3cb9kaLh7BqFnMUbonk";
            var bondingCurve = BonkBoundingCurveHelper.GetBondingCurvePda(new PublicKey(mint));
            // 6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX
            var config = BonkBoundingCurveHelper.GetGlobalPda(0,0);
            
            //FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1
            
            var accountInfo = await Config.rpcClient.GetAccountInfoAsync(bondingCurve);
           
            if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null)
            {
                Console.WriteLine($"无法获取bonding curve数据: {accountInfo.Reason}");
                return false;
            }

            var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
            var bondingCurveData = BonkBoundingCurveHelper.DecodeBondingCurveData(data);
            
            //内池交易参数,15个
            var payer = walletPublicKey;//1
            var authority = new PublicKey("WLHv2UAZm6z4KyaaELi5pjdbJh6RESMva1Rnn8pJVVh");
            var globalConfig = bondingCurveData.global_config;
            var platformConfig = bondingCurveData.platform_config;
            var poolState = bondingCurve;
            
            var  UserTokenPDA = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(walletPublicKey,new PublicKey(mint));
            var  UserWSolnPDA = BonkBoundingCurveHelper.GetUserQuoteATA(walletPublicKey, new PublicKey(Config.TOKEN_WSOL));
            
            var base_vault = bondingCurveData.base_vault;//8
            var quote_vault = bondingCurveData.quote_vault;
            var tokenMint = new PublicKey(mint);
            var tokenWSol = new PublicKey(Config.TOKEN_WSOL);//11
            
            var baseTokenProgram = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
            var baseWSOLProgram = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
            
            var eventAuthority = BonkBoundingCurveHelper.GetEventAuthorityPda();
            var program = BonkBoundingCurveHelper.ProgramId;//15
            
            
            var slippagePercent = 0.5D;
            var fee = 0.0D;
            
            double tokenPrice = (double)bondingCurveData.virtual_quote/(double)bondingCurveData.virtual_base;
            ulong tokenAmount = 1_000_000;
            PublicKey baseMintKey = tokenMint;
            PublicKey quoteMintKey = tokenWSol;
            globalConfig = globalConfig;
            PublicKey pool = poolState;
            PublicKey poolBaseTokenAccount = base_vault;
            PublicKey poolQuoteTokenAccount = quote_vault;
                

            // 基础SOL成本（1 SOL = 1,000,000,000 lamports）
            double baseSolCost = tokenPrice  * tokenAmount;
            
            Log.d($"买入:{tokenAmount}代币, SOL成本: {baseSolCost / 1_000_000_000.0:F9} SOL");
            
            // 计算包含滑点的最大SOL成本
            //TODO 动态计算价格
            double maxSolCost = SolUtil.CalculateMaxSolCostWithSlippage(baseSolCost, slippagePercent);
            // double maxSolCost = 1000000;
            
            Log.d($"最大SOL成本(含{slippagePercent}%滑点): {maxSolCost / 1_000_000_000.0:F9} SOL");

            
            // 设置最大交易计算单元
            uint units = 200_000;
            TransactionInstruction computeUnitLimitInstruction = ComputeBudgetHelper.SetComputeUnitLimit(units);

            // 创建优先交易费的指令
            double d = SolUtil.SolToLamp(fee);
            ulong priorityRate = (ulong)(d / units);
            TransactionInstruction priorityFeeInstruction = ComputeBudgetHelper.SetComputeUnitPrice(priorityRate);
            
            
           
            
            // 获取协议费接收者
            PublicKey protocolFeeRecipient = new PublicKey("7hTckgnGnLQR6sdH7YkqFTAA7VwTfYFaZ6EhEsU3saCX"); // 需要从链上获取
            PublicKey protocolFeeRecipientTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(
                protocolFeeRecipient, quoteMintKey);
            
            // 获取代币创建者
            PublicKey coinCreator = new PublicKey("ANimEfXzHpLxDMA4aqqjcX7ujMfozYzSTNZGnTr4X4NJ"); // 需要从链上获取
            MetaDataParser meta_fbc = await ChainTool.Meta.getTokenMetadataAccountAsync(Config.rpcClient,tokenMint);

            if (meta_fbc!=null&&meta_fbc.owner!=null&&meta_fbc.metadata!=null&&meta_fbc.metadata.creators!=null&&meta_fbc.metadata.creators.Count>0) {
                
                coinCreator = meta_fbc.metadata.creators[0].key;
            }else {
                Log.w($"获取代币元数据失败,tokenMint:{tokenMint}");
            }

            
            // 初始化钱包和客户端
            var account = WalletHelper.CreateAccountFromKeyPair(walletPrivateKey, walletPublicKey);
            // 获取用户代币账户 ,  72jyfCgTKZ7PbtkP1SHRyYDSauZohasUhBZhxYFwwyoZ
            PublicKey userBaseTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, baseMintKey);
            
            // 获取用户WSOL账户//AzcY5dwHdknr4LnWJPbzQ3xWQrWYaqikskVxD8X5xsYD
            PublicKey userQuoteTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, quoteMintKey);

            // 构建交易
            var lastBlockRsp = await Config.rpcClient.GetLatestBlockHashAsync();

            if (lastBlockRsp.WasSuccessful == false) {
                Log.w($"get last block hash failed");
                return false;
            }
            string recentBlockHash = lastBlockRsp.Result.Value.Blockhash;
            
            var txBuilder = new TransactionBuilder()
                .SetRecentBlockHash(recentBlockHash)
                .SetFeePayer(account)
                .AddInstruction(computeUnitLimitInstruction)
                .AddInstruction(priorityFeeInstruction);
            
            // 检查用户代币账户是否存在，如果不存在则创建
            var baseAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userBaseTokenAccount);
            if (!baseAccountInfo.WasSuccessful || baseAccountInfo.Result?.Value == null) {
                TransactionInstruction createBaseAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, baseMintKey);
                txBuilder.AddInstruction(createBaseAtaInstruction);
            }
            
            var quoteAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userQuoteTokenAccount);
            if (!quoteAccountInfo.WasSuccessful || quoteAccountInfo.Result?.Value == null) {
                TransactionInstruction createQuoteAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, quoteMintKey);
                txBuilder.AddInstruction(createQuoteAtaInstruction);
            }
            
            // 创建买入指令
            var instructionData = new List<byte>();
            instructionData.AddRange(BonkBoundingCurveHelper.Instructions.buyExactIn);
            
            ulong amount_B = (ulong)maxSolCost;
            instructionData.AddRange(BitConverter.GetBytes(amount_B)); // cost max  sol
            // ulong minimum_amount_out = tokenAmount;
            ulong minimum_amount_A = ***********;
            instructionData.AddRange(BitConverter.GetBytes(minimum_amount_A)); // minimum_amount_out
            ulong share_fee_rate = 0;
            instructionData.AddRange(BitConverter.GetBytes(share_fee_rate)); // share_fee_rate
            
            // 账户列表，按照IDL中的顺序
            var keys = new List<AccountMeta>
            {
                AccountMeta.Writable(account.PublicKey, true), // user
                AccountMeta.ReadOnly(authority, false),
                
                
                AccountMeta.ReadOnly(globalConfig, false),
                AccountMeta.ReadOnly(platformConfig, false),
                AccountMeta.Writable(pool, false),
                
                AccountMeta.Writable(userBaseTokenAccount, false), // user
                AccountMeta.Writable(userQuoteTokenAccount, false), // user
                
                AccountMeta.Writable(base_vault, false),
                AccountMeta.Writable(quote_vault, false),
                
                AccountMeta.ReadOnly(baseMintKey, false),
                AccountMeta.ReadOnly(quoteMintKey, false),
                
                
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false), //12 base_token_program
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false), //13 quote_token_program
                AccountMeta.ReadOnly(eventAuthority, false),//14
                
                AccountMeta.ReadOnly(BonkBoundingCurveHelper.ProgramId, false),//15
                
            };
            
            
            
            TransactionInstruction buyInstruction = new TransactionInstruction
            {
                Keys = keys,
                ProgramId = BonkBoundingCurveHelper.ProgramId,
                Data = instructionData.ToArray()
            };
            
            
            
            TransactionInstruction transferSolInstruction = SystemProgram.Transfer(
                account.PublicKey,
                userQuoteTokenAccount,
                (ulong)maxSolCost
            );
            
            TransactionInstruction syncNativeInstruction = new TransactionInstruction
            {
                ProgramId = TokenProgram.ProgramIdKey,
                Keys = new List<AccountMeta>
                {
                    AccountMeta.Writable(userQuoteTokenAccount, false)
                },
                Data = new byte[] { 17 } // SyncNative instruction
            };
            // 关闭账户指令
            TransactionInstruction closeAccountInstruction = SolUtil.buildCloseAccountInstruction(userQuoteTokenAccount, account);
            
            txBuilder.AddInstruction(transferSolInstruction);
            txBuilder.AddInstruction(syncNativeInstruction);
            txBuilder.AddInstruction(buyInstruction);
            txBuilder.AddInstruction(closeAccountInstruction);
            
            
            // 构建并发送交易
            var tx = txBuilder.Build(account);
            var result = await Config.rpcClient.SendTransactionAsync(tx);
            
            if (result.WasSuccessful) {
                Log.d($"买入交易成功，交易哈希: {result.Result}");
                    
                TransactionHistory.saveToDb(
                    walletPublicKey
                  , Config.TOKEN_SOL
                  , tokenMint
                  , solAmount:"-"+(baseSolCost /1_000_000_000D)
                  , mintAmout:tokenAmount.ToString(),txHash:result.Result);
                return true;
            } else {
                
                Log.d($"买入交易失败: {result.Reason}");
                return false;
            }

        }
       
    }
}
