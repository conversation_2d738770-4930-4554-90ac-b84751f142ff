using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Solnet.Programs;
using Solnet.Programs.PumpFunAmm;
using Solnet.Wallet;

namespace ChainTool;

public class FilpFlopBoundingCurveHelper {

    public static readonly PublicKey ProgramId = new PublicKey("LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj");
    
    public static PublicKey GetUserQuoteATA(PublicKey owner, PublicKey mint) {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { owner.KeyBytes, TokenProgram.ProgramIdKey.KeyBytes, mint.KeyBytes, },
            AssociatedTokenAccountProgram.ProgramIdKey, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    // export const LAUNCHPAD_AUTH_SEED = Buffer.from("vault_auth_seed", "utf8");
    // export const LAUNCHPAD_CONFIG_SEED = Buffer.from("global_config", "utf8");
    // export const LAUNCHPAD_POOL_SEED = Buffer.from("pool", "utf8");
    // export const LAUNCHPAD_POOL_VAULT_SEED = Buffer.from("pool_vault", "utf8");
    // export const LAUNCHPAD_POOL_VESTING_SEED = Buffer.from("pool_vesting", "utf8");
    // export const LAUNCHPAD_POOL_PLATFORM_SEED = Buffer.from("platform_config", "utf8");

    private const string LAUNCHPAD_AUTH_SEED = "vault_auth_seed"; //LAUNCHPAD_AUTH_SEED
    private const string GlobalSeed = "global_config";
    private const string BondingCurveSeed = "pool";
    private const string CPMM_POOL_SEED = "pool";
    private const string CreatorVaultSeed = "pool_vault"; //LAUNCHPAD_POOL_PLATFORM_SEED
    private const string LAUNCHPAD_POOL_VAULT_SEED = "pool_vesting";
    private const string LAUNCHPAD_POOL_PLATFORM_SEED = "platform_config";
    private const string VAULT_AUTH_SEED = "vault_auth_seed"; //118 97 117 108 116 95 97 117 116 104 95 115 101 101 100
    const string AMM_PROGRAM = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
    /// <summary>
    /// Event authority PDA seed
    /// </summary>
    private const string EventAuthoritySeed = "__event_authority";

    /// <summary>
    /// Instruction discriminators based on IDL
    /// </summary>
    public static class Instructions {
        public static readonly byte[] buyExactIn = { 250, 234, 13, 123, 213, 156, 19, 236 };
        public static readonly byte[] buyExactOut = { 24, 211, 116, 40, 105, 3, 153, 56 };
        public static readonly byte[] sellExactIn = { 149, 39, 222, 155, 211, 124, 152, 26 };
        public static readonly byte[] sellExactOut = { 95, 200, 71, 34, 8, 9, 11, 166 };
    }

    /// <summary>
    /// Calculate the global state PDA
    /// </summary>
    public static PublicKey GetGlobalPda(byte curveType = 0, ushort index = 0) {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> {
                Encoding.UTF8.GetBytes(GlobalSeed)
              , new PublicKey(Config.TOKEN_WSOL).KeyBytes
              , new byte[] { curveType }
              , BitConverter.GetBytes(index)
               ,
            },
            ProgramId, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    /// <summary>
    /// Calculate the bonding curve PDA for a given mint
    /// </summary>
    public static PublicKey GetBondingCurvePda(PublicKey mint) {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { Encoding.UTF8.GetBytes(BondingCurveSeed), mint.KeyBytes, new PublicKey(Config.TOKEN_WSOL).KeyBytes },
            ProgramId, out PublicKey outAddress, out byte bump);
        return outAddress;
    }
    
    public static void Test() {
        //BXifvnaiJPjhgNYDrcvvN8qK4aXniTtVtNpfzpodwHAG
        string mint = "xoTvq1X9XFRthKQTSawpwsAxjNUpgZqgD4XfLTMbonk";
        var amm_config = "D4FPEruKEHrG5TenZ2mpDGEfu1iUvTiqBxvpU8HLBvC2";

        for (ushort i = 0; i < 10; i++) {
             var amm_config1 = GetAmmConfigPDA(i);
             Console.WriteLine($"amm`: {amm_config1}");
        }
        // BXifvnaiJPjhgNYDrcvvN8qK4aXniTtVtNpfzpodwHAG
        var pool = GetCPMMPda(new PublicKey(mint), new PublicKey(amm_config));
        Console.WriteLine($"pool: {pool}");
    }
    
    public static PublicKey GetCPMMPda(PublicKey mint,PublicKey amm_config) {
        
        // var amm_config = GetAmmConfigPDA(0);
        
        
        
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { 
                Encoding.UTF8.GetBytes(CPMM_POOL_SEED), 
                amm_config.KeyBytes,
                new PublicKey(Config.TOKEN_WSOL).KeyBytes ,
                mint.KeyBytes, 
            },
            
            BonkFunProgram.CREATE_CPMM_POOL_PROGRAM,
            out PublicKey outAddress, out byte bump);
        return outAddress;
    }
    
    
    public static PublicKey GetAmmConfigPDA(ushort index) {
        // 将ushort转换为大端序的字节数组
        byte[] indexBytes = BitConverter.GetBytes(index);
        if (BitConverter.IsLittleEndian)
        {
            // 如果系统是小端序，则需要反转字节顺序
            Array.Reverse(indexBytes);
        }
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { 
                Encoding.UTF8.GetBytes("amm_config"), 
                indexBytes 
            },
            
            BonkFunProgram.CREATE_CPMM_POOL_PROGRAM, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    
    public static PublicKey GetPlatformConfigPDA(string admin) {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { Encoding.UTF8.GetBytes(LAUNCHPAD_POOL_PLATFORM_SEED), new PublicKey(admin).KeyBytes },
            ProgramId, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    /// <summary>
    /// Calculate the event authority PDA
    /// </summary>
    public static PublicKey GetEventAuthorityPda() {
        PublicKey.TryFindProgramAddress(
            new List<byte[]> { Encoding.UTF8.GetBytes(EventAuthoritySeed) },
            ProgramId, out PublicKey outAddress, out byte bump);
        return outAddress;
    }

    
    


    public static BonkBoundingCurveData DecodeBondingCurveData(byte[] data) =>BonkBoundingCurveData.Decode(data);

    public static async Task<BonkBoundingCurveData> GetBonkBoundingCurveData(string mint) {
        
        var bondingCurve = BonkBoundingCurveHelper.GetBondingCurvePda(new PublicKey(mint));
        var config = BonkBoundingCurveHelper.GetGlobalPda(0,0);
            
        var accountInfo = await Config.rpcClient.GetAccountInfoAsync(bondingCurve);
           
        if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null) {
            Console.WriteLine($"无法获取bonding curve数据: {accountInfo.Reason}");
            return null;
        }

        
        var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
        var bondingCurveData = BonkBoundingCurveHelper.DecodeBondingCurveData(data);
        bondingCurveData.tokenAmount = await SolUtil.GetTokenAccountBalance(bondingCurveData.base_vault);
        bondingCurveData.solAmount = await SolUtil.GetTokenAccountBalance(bondingCurveData.quote_vault);
        return bondingCurveData;
    }
}
