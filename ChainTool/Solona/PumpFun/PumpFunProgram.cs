using System;
using System.Collections.Generic;
using System.Text;
using Solnet.Programs;
using Solnet.Rpc.Models;
using Solnet.Wallet;

namespace ChainTool
{
    //https://github.com/rckprtr/pumpdotfun-sdk/blob/3507b88e7c6371ec42cc111226b6e07bc599b867/src/pumpfun.ts#L75
    
    /// <summary>
    /// Pump.fun Program implementation based on IDL
    /// Program ID: 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
    /// </summary>
    public static class PumpFunProgram
    {
        /// <summary>
        /// The Pump.fun program ID
        /// </summary>
        public static readonly PublicKey ProgramId = new("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");

        /// <summary>
        /// Global state PDA seed
        /// </summary>
        private const string GlobalSeed = "global";

        /// <summary>
        /// Bonding curve PDA seed
        /// </summary>
        private const string BondingCurveSeed = "bonding-curve";

        /// <summary>
        /// Event authority PDA seed
        /// </summary>
        private const string EventAuthoritySeed = "__event_authority";
        /// <summary>
        /// Creator vault PDA seed
        /// </summary>
        private const string CreatorVaultSeed = "creator-vault";

        /// <summary>
        /// Instruction discriminators based on IDL
        /// </summary>
        public static class Instructions
        {
            public static readonly byte[] Initialize = { 175, 175, 109, 31, 13, 152, 155, 237 };
            public static readonly byte[] SetParams = { 109, 201, 112, 181, 102, 181, 63, 249 };
            public static readonly byte[] Create = { 24, 30, 200, 40, 5, 28, 7, 119 };
            public static readonly byte[] Buy = { 102, 6, 61, 18, 1, 218, 235, 234 };
            public static readonly byte[] Sell = { 51, 230, 133, 164, 1, 127, 131, 173 };
            public static readonly byte[] Withdraw = { 183, 18, 70, 156, 148, 109, 161, 34 };
        }

        /// <summary>
        /// Calculate the global state PDA
        /// </summary>
        public static PublicKey GetGlobalPda()
        {

            PublicKey.TryFindProgramAddress(new List<byte[]> {
                Encoding.UTF8.GetBytes(GlobalSeed)
            },
            ProgramId,out PublicKey outAddress,out byte bump);
            return outAddress;
        }

        /// <summary>
        /// Calculate the bonding curve PDA for a given mint
        /// </summary>
        public static PublicKey GetBondingCurvePda(PublicKey mint)
        {
            PublicKey.TryFindProgramAddress(new List<byte[]> {
                Encoding.UTF8.GetBytes(BondingCurveSeed), mint.KeyBytes
            },
            ProgramId,out PublicKey outAddress,out byte bump);
            return outAddress;
        }

        /// <summary>
        /// Calculate the event authority PDA
        /// </summary>
        public static PublicKey GetEventAuthorityPda()
        {
            PublicKey.TryFindProgramAddress(new List<byte[]> {
                Encoding.UTF8.GetBytes(EventAuthoritySeed)
            },
            ProgramId,out PublicKey outAddress,out byte bump);
            return outAddress;
        }

        /// <summary>
        /// Calculate the creator vault PDA for a given bonding curve creator
        /// </summary>
        /// <param name="bondingCurveCreator">The creator field from the bonding curve account</param>
        /// <returns>The creator vault PDA</returns>
        public static PublicKey GetCreatorVaultPda(PublicKey bondingCurveCreator)
        {
            PublicKey.TryFindProgramAddress(new List<byte[]> {
                Encoding.UTF8.GetBytes(CreatorVaultSeed),
                bondingCurveCreator.KeyBytes
            },
            ProgramId, out PublicKey outAddress, out byte bump);
            
            return outAddress;
        }

        /// <summary>
        /// Create a buy instruction for Pump.fun
        /// </summary>
        /// <param name="user">The user account (buyer)</param>
        /// <param name="mint">The token mint to buy</param>
        /// <param name="tokenCreator">the token Creator</param>
        /// <param name="amount">Amount of tokens to buy</param>
        /// <param name="maxSolCost">Maximum SOL to spend (slippage protection)</param>
        /// <param name="feeRecipient">Fee recipient account</param>
        /// <returns>Transaction instruction</returns>
        public static TransactionInstruction CreateBuyInstruction(
            PublicKey user,
            PublicKey mint,
            PublicKey tokenCreator,
            ulong amount,
            ulong maxSolCost,
            PublicKey feeRecipient)
        {
            // 计算PDAs
            var global = GetGlobalPda();
            var bondingCurve = GetBondingCurvePda(mint);
            
            // 这里我们需要获取bonding_curve.creator的值
            // 理想情况下，我们应该从链上获取BondingCurve账户数据
            // 但为了简化，我们可以使用一个已知的creator地址
            // 在实际应用中，您应该先获取BondingCurve账户数据，然后提取creator字段
            
            // 计算creator_vault PDA
            var creatorVault = GetCreatorVaultPda(tokenCreator);
            
            var associatedBondingCurve = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(bondingCurve, mint);
            var associatedUser = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(user, mint);
            var eventAuthority = GetEventAuthorityPda();
            // var eventAuthority = new PublicKey("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1");
            
            // 构建指令数据
            var instructionData = new List<byte>();
            instructionData.AddRange(Instructions.Buy);
            instructionData.AddRange(BitConverter.GetBytes(amount));
            instructionData.AddRange(BitConverter.GetBytes(maxSolCost));

            // 严格按照IDL中的账户顺序构建账户列表
            var accounts = new List<AccountMeta>
            {
                AccountMeta.ReadOnly(global, false),                    // 1.global
                AccountMeta.Writable(feeRecipient, false),              // 2.feeRecipient
                AccountMeta.ReadOnly(mint, false),                      // 3.mint
                AccountMeta.Writable(bondingCurve, false),              // 4.bondingCurve
                AccountMeta.Writable(associatedBondingCurve, false),    // 5.associatedBondingCurve
                AccountMeta.Writable(associatedUser, false),            // 6.associatedUser
                AccountMeta.Writable(user, true),                       // 7.user (signer)
                AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false), // 8.systemProgram
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false),  // 9.tokenProgram
                AccountMeta.Writable(creatorVault, false),              // 10.creator_vault - 确保标记为可变(Writable)
                AccountMeta.ReadOnly(eventAuthority, false),            // 11.eventAuthority
                AccountMeta.ReadOnly(ProgramId, false)                  // 12.program
            };

            byte[] tranSerDatas = instructionData.ToArray();
            Console.WriteLine($"交易数据: {BitConverter.ToString(tranSerDatas)}");
            Console.WriteLine($"Creator Vault地址: {creatorVault}");
            
            return new TransactionInstruction
            {
                ProgramId = ProgramId,
                Keys = accounts,
                Data = tranSerDatas
            };
        }

        /// <summary>
        /// Create a sell instruction for Pump.fun
        /// </summary>
        /// <param name="user">The user account (seller)</param>
        /// <param name="mint">The token mint to sell</param>
        /// <param name="amount">Amount of tokens to sell</param>
        /// <param name="minSolOutput">Minimum SOL to receive (slippage protection)</param>
        /// <param name="feeRecipient">Fee recipient account</param>
        /// <returns>Transaction instruction</returns>
        public static TransactionInstruction CreateSellInstruction(
            PublicKey user,
            PublicKey mint,
            PublicKey tokenCreator,
            ulong amount,
            ulong minSolOutput,
            PublicKey feeRecipient)
        {
            // Calculate PDAs
            var global = GetGlobalPda();
            var bondingCurve = GetBondingCurvePda(mint);
            var creatorVault = GetCreatorVaultPda(tokenCreator);
            var associatedBondingCurve = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(bondingCurve, mint);
            var associatedUser = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(user, mint);
            var eventAuthority = GetEventAuthorityPda();

            // Build instruction data
            var instructionData = new List<byte>();
            instructionData.AddRange(Instructions.Sell);
            instructionData.AddRange(BitConverter.GetBytes(amount));
            instructionData.AddRange(BitConverter.GetBytes(minSolOutput));

            // Build accounts list according to IDL
            var accounts = new List<AccountMeta>
            {
                AccountMeta.ReadOnly(global, false),                    // global
                AccountMeta.Writable(feeRecipient, false),             // feeRecipient
                AccountMeta.ReadOnly(mint, false),                     // mint
                AccountMeta.Writable(bondingCurve, false),             // bondingCurve
                AccountMeta.Writable(associatedBondingCurve, false),   // associatedBondingCurve
                AccountMeta.Writable(associatedUser, false),           // associatedUser
                AccountMeta.Writable(user, true),                      // user (signer)
                AccountMeta.ReadOnly(SystemProgram.ProgramIdKey, false), // systemProgram
                AccountMeta.Writable(creatorVault, false),  // tokenProgram
                AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false), // associatedTokenProgram
                AccountMeta.ReadOnly(eventAuthority, false),           // eventAuthority
                AccountMeta.ReadOnly(ProgramId, false)                 // program
            };

            return new TransactionInstruction
            {
                ProgramId = ProgramId,
                Keys = accounts,
                Data = instructionData.ToArray()
            };
        }

        /// <summary>
        /// Helper method to get the fee recipient from global state
        /// You would need to fetch this from the blockchain in a real implementation
        /// </summary>
        public static PublicKey GetDefaultFeeRecipient()
        {
            
            //todo  获取最新节点的fee recipient
            
            // async getGlobalAccount(commitment: Commitment = DEFAULT_COMMITMENT) {
            //     const [globalAccountPDA] = PublicKey.findProgramAddressSync(
            //         [Buffer.from(GLOBAL_ACCOUNT_SEED)],
            //         new PublicKey(PROGRAM_ID)
            //     );
            //
            //     const tokenAccount = await this.connection.getAccountInfo(
            //         globalAccountPDA,
            //         commitment
            //     );
            //
            //     return GlobalAccount.fromBuffer(tokenAccount!.data);
            // }
            // This is a placeholder - in a real implementation, you would fetch this from the global state
            // For now, return a known fee recipient address
            return new PublicKey("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM");
        }


    }
}
