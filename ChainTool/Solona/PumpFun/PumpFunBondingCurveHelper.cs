using System;
using System.Threading.Tasks;
using Solnet.Rpc;
using Solnet.Rpc.Models;
using Solnet.Wallet;

namespace ChainTool
{
    /// <summary>
    /// Pump.fun Bonding Curve 辅助类
    /// 用于查询和计算bonding curve相关信息
    /// </summary>
    public class PumpFunBondingCurveHelper
    {
        private readonly IRpcClient _rpcClient;

        public PumpFunBondingCurveHelper(IRpcClient rpcClient)
        {
            _rpcClient = rpcClient;
        }

        /// <summary>
        /// Bonding Curve 账户数据结构
        /// 基于IDL中的BondingCurve结构
        /// </summary>
        public class BondingCurveData
        {
            public ulong VirtualTokenReserves { get; set; }
            public ulong VirtualSolReserves { get; set; }
            public ulong RealTokenReserves { get; set; }
            public ulong RealSolReserves { get; set; }
            public ulong TokenTotalSupply { get; set; }
            public bool Complete { get; set; }
            public double CurrentPrice { get; set; } //lamp为单位
        }

        /// <summary>
        /// 获取bonding curve的状态信息
        /// </summary>
        /// <param name="mint">代币mint地址</param>
        /// <returns>Bonding curve数据，如果获取失败返回null</returns>
        public async Task<BondingCurveData> GetBondingCurveDataAsync(PublicKey mint)
        {
            try
            {
                var bondingCurvePda = PumpFunProgram.GetBondingCurvePda(mint);
                var accountInfo = await _rpcClient.GetAccountInfoAsync(bondingCurvePda);

                if (!accountInfo.WasSuccessful || accountInfo.Result?.Value?.Data == null)
                {
                    Console.WriteLine($"无法获取bonding curve数据: {accountInfo.Reason}");
                    return null;
                }

                var data = Convert.FromBase64String(accountInfo.Result.Value.Data[0]);
                return DecodeBondingCurveData(data);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取bonding curve数据时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解码bonding curve账户数据
        /// </summary>
        /// <param name="data">账户数据字节数组</param>
        /// <returns>解码后的bonding curve数据</returns>
        private BondingCurveData DecodeBondingCurveData(byte[] data)
        {
            if (data.Length < 41) // 最小数据长度检查
            {
                throw new ArgumentException("Bonding curve数据长度不足");
            }

            var bondingCurve = new BondingCurveData();
            
            // 根据Rust结构体布局解析数据
            // 注意：这里的偏移量需要根据实际的Rust结构体布局调整
            int offset = 0;
            
            UInt64 unkonwn = BitConverter.ToUInt64(data, offset);
            
            offset += 8;
            
            bondingCurve.VirtualTokenReserves = BitConverter.ToUInt64(data, offset);
            offset += 8;
            
            bondingCurve.VirtualSolReserves = BitConverter.ToUInt64(data, offset);
            offset += 8;
            
            bondingCurve.RealTokenReserves = BitConverter.ToUInt64(data, offset);
            offset += 8;
            
            bondingCurve.RealSolReserves = BitConverter.ToUInt64(data, offset);
            offset += 8;
            
            bondingCurve.TokenTotalSupply = BitConverter.ToUInt64(data, offset);
            offset += 8;
            
            bondingCurve.Complete = data[offset] != 0;
            bondingCurve.CurrentPrice = GetCurrentPrice(bondingCurve);
            return bondingCurve;
        }

        /// <summary>
        /// 计算购买指定数量代币需要的SOL数量
        /// 使用恒定乘积公式 (x * y = k)
        /// </summary>
        /// <param name="bondingCurve">当前bonding curve状态</param>
        /// <param name="tokenAmount">要购买的代币数量</param>
        /// <returns>需要的SOL数量（lamports）</returns>
        public double CalculateBuyPrice(BondingCurveData bondingCurve, ulong tokenAmount)
        {
            if (bondingCurve == null)
            {
                throw new ArgumentNullException(nameof(bondingCurve));
            }

            if (bondingCurve.Complete)
            {
                throw new InvalidOperationException("Bonding curve已完成，无法继续交易");
            }

            return bondingCurve.CurrentPrice*tokenAmount;
            
            // ulong bondingCurveRealTokenReserves = bondingCurve.TokenTotalSupply - bondingCurve.RealTokenReserves;
            // return (ulong)CalculateTotalPrice((int)bondingCurveRealTokenReserves, (int)tokenAmount);
            
            
            // 使用虚拟储备计算价格
            // 恒定乘积公式: (virtualSolReserves + solIn) * (virtualTokenReserves - tokenOut) = k
            // 其中 k = virtualSolReserves * virtualTokenReserves
            
            var k = bondingCurve.VirtualSolReserves * bondingCurve.VirtualTokenReserves;
            var newTokenReserves = bondingCurve.VirtualTokenReserves - tokenAmount;
            
            if (newTokenReserves <= 0)
            {
                throw new InvalidOperationException("购买数量超过可用代币储备");
            }
            
            var newSolReserves = k / newTokenReserves;
            var solNeeded = newSolReserves - bondingCurve.VirtualSolReserves;
            
            return solNeeded;
        }

        /// <summary>
        /// 计算出售指定数量代币可获得的SOL数量
        /// </summary>
        /// <param name="bondingCurve">当前bonding curve状态</param>
        /// <param name="tokenAmount">要出售的代币数量</param>
        /// <returns>可获得的SOL数量（lamports）</returns>
        public ulong CalculateSellPrice(BondingCurveData bondingCurve, ulong tokenAmount)
        {
            if (bondingCurve == null)
            {
                throw new ArgumentNullException(nameof(bondingCurve));
            }

            if (bondingCurve.Complete)
            {
                throw new InvalidOperationException("Bonding curve已完成，无法继续交易");
            }

            // 使用虚拟储备计算价格
            var k = bondingCurve.VirtualSolReserves * bondingCurve.VirtualTokenReserves;
            var newTokenReserves = bondingCurve.VirtualTokenReserves + tokenAmount;
            var newSolReserves = k / newTokenReserves;
            var solReceived = bondingCurve.VirtualSolReserves - newSolReserves;
            
            return solReceived;
        }

        /// <summary>
        /// 获取代币当前价格（每个代币的SOL价格）
        /// </summary>
        /// <param name="bondingCurve">当前bonding curve状态</param>
        /// <returns>每个代币的SOL价格（以lamports为单位）</returns>
        public double GetCurrentPrice(BondingCurveData bondingCurve)
        {
            if (bondingCurve == null)
            {
                throw new ArgumentNullException(nameof(bondingCurve));
            }

            // 价格 = SOL储备 / 代币储备 / 1000 (代币单位为  1 mlamp, sol单位为1000 mlamp, 得出的价格以lamp为单位.)
            return (double)bondingCurve.VirtualSolReserves / bondingCurve.VirtualTokenReserves ;
        }

        /// <summary>
        /// 打印bonding curve状态信息
        /// </summary>
        /// <param name="bondingCurve">bonding curve数据</param>
        /// <param name="tokenMint">代币mint地址</param>
        public void PrintBondingCurveInfo(BondingCurveData bondingCurve, string tokenMint)
        {
            if (bondingCurve == null)
            {
                Console.WriteLine("Bonding curve数据为空");
                return;
            }

            Console.WriteLine($"=== Bonding Curve 信息 - {tokenMint} ===");
            Console.WriteLine($"虚拟代币储备: {bondingCurve.VirtualTokenReserves:N0} mlamp");
            Console.WriteLine($"虚拟SOL储备: {bondingCurve.VirtualSolReserves :F4} lamp");
            Console.WriteLine($"真实代币储备: {bondingCurve.RealTokenReserves:N0} mlamp");
            Console.WriteLine($"真实SOL储备: {bondingCurve.RealSolReserves :F4} lamp");
            Console.WriteLine($"代币总供应量: {bondingCurve.TokenTotalSupply:N0} mlamp");
            Console.WriteLine($"是否完成: {(bondingCurve.Complete ? "是" : "否")}");
            Console.WriteLine($"当前价格: {bondingCurve.CurrentPrice:F9} lamp/token(mlamp)");
            Console.WriteLine("=====================================");
        }
        
        
        

        /// <summary>
        /// 计算购买k个代币的总价格
        /// </summary>
        /// <param name="sold">当前已售出代币数量</param>
        /// <param name="buyAmount">买入的代币数量</param>
        /// <param name="initialPrice">代币初始价格（SOL），通常为0.000001</param>
        /// <param name="priceStep">价格增量（SOL），通常为0.000001</param>
        /// <returns>总价格（SOL）</returns>
        public static decimal CalculateTotalPrice(int sold, int buyAmount,decimal initialPrice = 0.000001m, decimal priceStep = 0.000001m)
        {
            // 总价 = k * 初始价 + 增量 * [k * n + k*(k+1)/2]
            decimal k = buyAmount;
            decimal n = sold;
            decimal total = k * initialPrice + priceStep * (k * n + (k * (k + 1)) / 2);
            return total;
        }

        /// <summary>
        /// 计算当前第n个代币的单价
        /// </summary>
        /// <param name="n">当前是第n个代币（从0开始）</param>
        /// <returns>单价（SOL）</returns>
        public static decimal CalculateTokenPrice(int n,decimal initialPrice = 0.000001m, decimal priceStep = 0.000001m)
        {
            return initialPrice + n * priceStep;
        }
        
    }
}
