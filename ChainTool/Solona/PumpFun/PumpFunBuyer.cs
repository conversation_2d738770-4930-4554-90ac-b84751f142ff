using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Solnet.Programs.TokenSwap;
using Solnet.Rpc;
using Solnet.Rpc.Builders;
using Solnet.Wallet;
using Solnet.Programs;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Solnet.Rpc.Core.Http;
using Solnet.Rpc.Models;
using ChainTool.Utils;
using ChainTool.Models;
using Solnet.Metaplex.NFT.Library;
using Wallet = Solnet.Wallet.Wallet;

namespace ChainTool;

//  pump.fun 是基于 TokenSwap 实现
//
// 在Solana中使用lamports作为单位：
// Lamports的基本概念：
// Lamports是Solana区块链上的最小货币单位
// 1 SOL = 1,000,000,000 lamports（10亿lamports）
// 这个命名是为了纪念Leslie Lamport，他是分布式系统的先驱

// 1000000 lamports = 0.001 SOL
// 如果我们要买入1 SOL，就需要设置为1000000000 lamports
// 如果我们要买入0.1 SOL，就需要设置为100000000 lamports
public class BuyPumpFunExample
{

    const string walletPublicKey = "9Tkq9jeo5mg6pXmR18qFT2hYuxE2aMaycLfSGKbH6hp3";
    //钱包wallet对应FBC的PDA地址, 用于交易FBC币
    const string walletFBC = "d1xheXNGW3dN82AkcrQoJXTTVGQB8wfPjeKZkEiveU6";
    
    const string programId = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
    const string tokenPublicKey = "7dSve9rvX3SPp2ES2Dr3jdrYKu6fF7yLnafDnjGSpump";
    const string OwnerProgram = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
    const string MetaProgram = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s";
    const string AccountPublicKey = "********************************************";
    public readonly static IRpcClient rpcClient = ClientFactory.GetClient("https://staked.helius-rpc.com?api-key=4ad36efe-8eb1-4b2d-86ea-9c09b9b2093e");

    public static void Test() {
        string  tokenProgramOwnerId="TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
        
        // Meta.genGlobalPDA(new PublicKey(programId),out PublicKey globalPDA, out byte bump);
        string createAccount = testTokenMetadataAccount();
        // string tokenProgramOwnerId = getTokenProgramOwnerID(tokenPublicKey);

        // getBalance(rpcClient,walletPublicKey);
        // getProgramAccountList(tokenProgramOwnerId,tokenPublicKey);
        
        getTokenAccountInWallet(rpcClient,walletPublicKey,tokenPublicKey,tokenProgramOwnerId);
            
            
        // var rsp = rpcClient.GetAccountInfo("Gy9PDqJrmRuCsnwZ48Yia6ks9FpnM87bAx8cSkNWSidg");
        var rsp = rpcClient.GetAccountInfo(programId);
        if (rsp.Result?.Value?.Data == null)
        {
            Console.WriteLine("获取账户数据失败");
            return;
        }

        
        Console.WriteLine("press any key to continue...");
        Console.ReadLine();
    }
    //获取token的meta数据结构 和 owner Account
    public static  string testTokenMetadataAccount() {
        
        MetaDataParser meta_fbc = Meta.getTokenMetadataAccount(rpcClient,tokenPublicKey);
        return meta_fbc.accInfo.Owner;
        // MetadataAccount meta_sol =Meta. getTokenMetadataAccount(rpcClient,"So11111111111111111111111111111111111111111");
        // MetadataAccount meta_wsol = Meta.getTokenMetadataAccount(rpcClient,"So11111111111111111111111111111111111111112");
    }
    // 通过私钥创建账户
    public Account CreateAccountFromPrivateKey(string publicKey,string privateKey)
    {
        // 检查私钥格式
        if (string.IsNullOrEmpty(privateKey))
        {
            throw new ArgumentException("私钥不能为空");
        }

        try
        {
            // 将Base58格式的私钥转换为字节数组
            byte[] privateKeyBytes = Base58.FromBase58String(privateKey);
            // 创建账户
            return new Account(privateKey,publicKey);
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"无效的私钥格式: {ex.Message}");
        }
    }
    public static  string getTokenProgramOwnerID(string token) {
        var accountInfo =  rpcClient.GetAccountInfo(token);
        var ownerProgramId = accountInfo?.Result?.Value?.Owner??"";
        Console.WriteLine("Token owner Program ID: " + ownerProgramId);
        return ownerProgramId;
    }
    public static async Task Test2()
        {
            // Helius API 的 URL
            string url = "https://rpc.helius-rpc.com/v0"; // 替换为您的 Helius 端点
            string apiKey = "4ad36efe-8eb1-4b2d-86ea-9c09b9b2093e"; // 替换为您的 Helius API 密钥

            // Program ID
            string programId = Config.Program_PumpFun; // 替换为您要查询的 Program ID

            // 构建请求负载
            var requestData = new
            {
                jsonrpc = "2.0",
                id = 1,
                method = "getProgramAccounts",
                @params = new object[]
                {
                    programId,
                    new
                    {
                        encoding = "base64",
                        filters = new object[]
                        {
                            new { dataSize = 165 } // 过滤条件，数据大小为 165 字节
                        }
                    }
                }
            };

            // 将请求负载序列化为 JSON 格式
            string jsonPayload = JsonSerializer.Serialize(requestData);

            // 初始化 HTTP 客户端
            using (HttpClient client = new HttpClient())
            {
                // 设置请求头
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
                client.DefaultRequestHeaders.Add("Accept", "application/json");

                // 创建 HTTP 请求
                HttpContent content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                try
                {
                    // 发送 POST 请求
                    HttpResponseMessage response = await client.PostAsync(url, content);

                    // 确保响应成功
                    response.EnsureSuccessStatusCode();

                    // 获取响应体内容
                    string responseBody = await response.Content.ReadAsStringAsync();
                    Console.WriteLine("API 响应:");
                    Console.WriteLine(responseBody);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("请求失败:");
                    Console.WriteLine(ex.Message);
                }
            }
        }
    public static void getProgramAccountList(string tokenProgramOwnerId,string tokenAddress )
    {
        try
        {
            Console.WriteLine($"查找Program:{tokenProgramOwnerId}, 针对代币:{tokenAddress}的交易账号");
            var accounts =  getAccountByProgram(rpcClient,tokenProgramOwnerId,tokenAddress);
            
            if (accounts != null) {
                // AccountKeyPair swapAccount =accounts.FirstOrDefault(a =>
                //     a.Account.Owner == programId &&
                //     a.Account.Data.Count > 0);
                Console.WriteLine($"找到的账号数量:{accounts.Count}");
                foreach (AccountKeyPair keyPair in accounts) {
                    Console.WriteLine($"AccountKeyPair.PublicKey: {keyPair.PublicKey}");
                    
                    Console.WriteLine($"找到 {OwnerProgram} 的代币账号: PublicKey={keyPair.PublicKey}, Owner={keyPair.Account.Owner} ,Lamports:{keyPair.Account.Lamports}");
                    Console.WriteLine($"data(代币交换参数): {keyPair.Account.Data[0]}");
                    
                    // 将 base64 编码的数据转换为字节数组
                    var accountData = Convert.FromBase64String(keyPair.Account.Data[0]);
                    var tokenSwapInfo = TokenSwapLayout.Decode(accountData);

                    Console.WriteLine($"解码代币账号的代币交换参数, 协议版本 :{ tokenSwapInfo.GetType()}");
                    Console.WriteLine($"  Version: {tokenSwapInfo.isInitialized}");
                    Console.WriteLine($"  TokenAccountA: {tokenSwapInfo.TokenAccountA}");
                    Console.WriteLine($"  TokenAccountB: {tokenSwapInfo.TokenAccountB}");
                    Console.WriteLine($"  PoolMint: {tokenSwapInfo.tokenPool}");
                    Console.WriteLine($"  feesNumerator: {tokenSwapInfo.feesNumerator}");
                    Console.WriteLine($"  feeDenominator: {tokenSwapInfo.feeDenominator}");
                    
                }

            } else {
                Console.WriteLine("未找到TokenSwap账户");
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"查询失败: {ex.Message}");
        }
    }

    // 根据ProgramID查询他管理的所有账号信息
    // 使用过滤器查询账户
    private static  List<AccountKeyPair> getAccountByProgram(IRpcClient rpcClient,string programId,string tokenPublicKey) {
        
        var memCmpList = new List<MemCmp>();
        var memCmp = new MemCmp();
        memCmp.Offset= 0; // 偏移量，用于匹配 Token Mint 地址
        memCmp.Bytes= tokenPublicKey; // 目标 Token 的 Mint 地址
        memCmpList.Add(memCmp);
        var accounts =  rpcClient.GetProgramAccounts(
            programId,
            dataSize: 165 ,
            memCmpList: memCmpList,
            commitment: Solnet.Rpc.Types.Commitment.Confirmed
        );
            
        // 过滤出符合条件的账户
        if (accounts.Result == null) {
            Console.WriteLine("获取Program账号失败");
            return null;
        }
       
        return accounts.Result;
    }

    //查询钱包余额
    public static bool getBalance(IRpcClient rpcClient, string walletPublicKey) {
        var balance = rpcClient.GetBalance(walletPublicKey);
        if (balance == null||balance.Result==null) {
            Console.WriteLine("获取余额失败");
            return false;
        }
        Console.WriteLine($"balance: {balance.Result.Value}");
        return true;
    }

    //获取钱包下关于此代理的子账号. 用于该代币的交易
    public static void  getTokenAccountInWallet(IRpcClient rpcClient, string wallet,string token,string programId)
    {

        // 3. 调用 getTokenAccountsByOwner 方法
        var tokenAccountsResult =  rpcClient.GetTokenAccountsByOwner(
            wallet,
            token,
            null
            // programId
        );

        // 4. 检查请求是否成功
        if (tokenAccountsResult.WasSuccessful && tokenAccountsResult.Result.Value != null)
        {
            Console.WriteLine("找到的账户列表:");

            // 遍历所有账户
            foreach (var account in tokenAccountsResult.Result.Value)
            {
                // 打印每个账户的公钥
                Console.WriteLine($"账户 PublicKey: {account.PublicKey}");
            }
        }
        else
        {
            Console.WriteLine($"未找到与 Mint Address 匹配的账户: {tokenAccountsResult.Reason}");
        }
    }




    public void Swap()
    {
        try
        {
            // // 设置代理环境变量
            // Environment.SetEnvironmentVariable("http_proxy", "http://your-proxy-address:port");
            // Environment.SetEnvironmentVariable("https_proxy", "http://your-proxy-address:port");

            // 1. 通过私钥初始化账户
            string publicKey = "你的公钥";
            string privateKey = "你的私钥";
            Account account = CreateAccountFromPrivateKey(publicKey,privateKey);

            // 2. 定义相关账户和池信息
            string tokenSwapAccountPubKey = "TokenSwapAccount公钥"; // 需要替换为实际的池swap账号
            string swapAuthorityPubKey = "SwapAuthority公钥";       // 需要替换为实际的池权限账号
            string userTransferAuthority = account.PublicKey.ToString();
            string poolTokenMint = "池LP token mint地址";           // 需要替换为实际的LP token地址
            string feeAccount = "池fee账号";                       // 需要替换为实际的fee账号
            string hostFeeAccount = "host fee账号";                // 需要替换为实际的host fee账号
            string sourceTokenAccount = "你的SOL Token账户";        // 需要替换为实际的SOL token账户
            string destinationTokenAccount = "你的pump.fun Token账户"; // 需要替换为实际的pump.fun token账户
            string inputTokenMint = "So11111111111111111111111111111111111111112"; // SOL的mint地址
            string pumpFunMint = "7dSve9rvX3SPp2ES2Dr3jdrYKu6fF7yLnafDnjGSpump"; // pump.fun的mint地址

            // 3. 构造Swap交易指令
            var tokenSwapProgram = new TokenSwapProgram();
            var swapInstruction = tokenSwapProgram.Swap(
                new PublicKey(tokenSwapAccountPubKey),
                new PublicKey(userTransferAuthority),
                new PublicKey(sourceTokenAccount),
                new PublicKey(swapAuthorityPubKey),
                new PublicKey(destinationTokenAccount),
                new PublicKey(poolTokenMint),
                new PublicKey(feeAccount),
                new PublicKey(hostFeeAccount),
                new PublicKey(hostFeeAccount), // poolTokenHostFeeAccount
                1000000,   // 买入数量（以lamports为单位，1 SOL = 1,000,000,000 lamports）
                1         // 最少收到的pump.fun数量
            );

            // 4. 构造交易
            var rpcClient = ClientFactory.GetClient(Cluster.MainNet);
            var recentBlockHash = rpcClient.GetLatestBlockHash();

            var txBuilder = new TransactionBuilder()
               .SetRecentBlockHash(recentBlockHash.Result.Value.Blockhash)
               .SetFeePayer(account.PublicKey)
               .AddInstruction(swapInstruction);

            var tx = txBuilder.Build(account);

            // 5. 发送交易
            var result = rpcClient.SendTransaction(tx);

            Console.WriteLine(result.WasSuccessful
                ? $"买入成功，交易哈希: {result.Result}"
                : $"买入失败: {result.Reason}");
        }
        finally
        {
            // // 清除代理设置
            // Environment.SetEnvironmentVariable("http_proxy", null);
            // Environment.SetEnvironmentVariable("https_proxy", null);
        }
    }
}