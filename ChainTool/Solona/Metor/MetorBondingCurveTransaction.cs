using System.Threading.Tasks;
using Solnet.Programs;

namespace ChainTool;

public class MetorBondingCurveTransaction : ITokenTransaction {

    public Task<bool> buy(string walletPublicKey
      , string walletPrivateKey
      , string mint
      , ulong tokenAmount
      , double tokenPrice
      , double slippagePercent = 5
      , double fee = 0
    ) {
        throw new System.NotImplementedException();
    }

    public Task<bool> sell(string walletPublicKey
      , string walletPrivateKey
      , string tokenMint
      , ulong tokenAmount
      , double tokenPrice
      , double slippagePercent = 5
      , double fee = 0
    ) {
        throw new System.NotImplementedException();
    }
}
