using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using ChainTool.Models;
using Solnet.KeyStore;
using Solnet.Programs;
using Solnet.Rpc.Builders;
using Solnet.Rpc.Models;
using Solnet.Wallet;
using Log = Shared.Log;

namespace ChainTool;

public class BonkCpmmTransaction : ITokenTransaction {

    public async Task<bool> buy(string walletPublicKey
      , string walletPrivateKey
      , string mint
      , ulong tokenAmount
      , double tokenPrice
      , double slippagePercent = 5
      , double fee = 0
    ) {
        try {
            if (string.IsNullOrEmpty(walletPublicKey) ||
                string.IsNullOrEmpty(walletPrivateKey) ||
                string.IsNullOrEmpty(mint) ||
                tokenPrice < 0 ||
                slippagePercent < 0 ||
                fee < 0) {
                Log.w($"buy fail,参数错误,{walletPublicKey},{walletPrivateKey},{mint},{tokenPrice},{slippagePercent},{fee}");
                return false;
            }

            tokenAmount = tokenAmount * 1_000_000;

            BonkFunProgram.cachedPoolData.TryGetValue(mint, out var poolData);

            if (poolData == null) {
                Log.w($"获取poolData失败,{mint}");
                return false;
            }
            


            var wallet = new PublicKey(walletPublicKey);

            //内池交易参数,15个
            var payer = walletPublicKey; //1
            var authority = new PublicKey("GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL");
            var amm_config = poolData.amm_config;
            var poolState = poolData;

            var UserTokenPDA = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(wallet, new PublicKey(mint));
            var UserWSolnPDA = BonkBoundingCurveHelper.GetUserQuoteATA(wallet, new PublicKey(Config.TOKEN_WSOL));

            var mint_vault = poolData.token_1_vault; //8
            var wsol_vault = poolData.token_0_vault;
            var tokenMint = new PublicKey(mint);
            var tokenWSol = new PublicKey(Config.TOKEN_WSOL); //11


            tokenPrice = poolData.price();

            PublicKey baseMintKey = tokenMint;
            PublicKey quoteMintKey = tokenWSol;

            // 基础SOL成本（1 SOL = 1,000,000,000 lamports）
            double baseSolCost = tokenPrice * tokenAmount;

            Log.d($"买入:{tokenAmount}代币, SOL成本: {baseSolCost / 1_000_000_000.0:F9} SOL");

            // 计算包含滑点的最大SOL成本
            double maxSolCost = SolUtil.CalculateMaxSolCostWithSlippage(baseSolCost, slippagePercent);
            // double maxSolCost = 1000000;
            Log.d($"最大SOL成本(含{slippagePercent}%滑点): {maxSolCost / 1_000_000_000.0:F9} SOL");

            // 设置最大交易计算单元
            uint units = 200_000;
            TransactionInstruction computeUnitLimitInstruction = ComputeBudgetHelper.SetComputeUnitLimit(units);

            // 创建优先交易费的指令
            double d = SolUtil.SolToLamp(fee);
            ulong priorityRate = (ulong)(d / units);
            TransactionInstruction priorityFeeInstruction = ComputeBudgetHelper.SetComputeUnitPrice(priorityRate);

            // 初始化钱包和客户端
            var account = WalletHelper.CreateAccountFromKeyPair(walletPrivateKey, walletPublicKey);
            // 获取用户代币账户 ,  72jyfCgTKZ7PbtkP1SHRyYDSauZohasUhBZhxYFwwyoZ
            PublicKey userBaseTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, tokenMint);

            // 获取用户WSOL账户//AzcY5dwHdknr4LnWJPbzQ3xWQrWYaqikskVxD8X5xsYD
            PublicKey userQuoteTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, tokenWSol);

            // 构建交易
            var lastBlockRsp = await Config.rpcClient.GetLatestBlockHashAsync();

            if (lastBlockRsp.WasSuccessful == false) {
                Log.w($"get last block hash failed");
                return false;
            }

            string recentBlockHash = lastBlockRsp.Result.Value.Blockhash;

            var txBuilder = new TransactionBuilder()
               .SetRecentBlockHash(recentBlockHash)
               .SetFeePayer(account)
               .AddInstruction(computeUnitLimitInstruction)
               .AddInstruction(priorityFeeInstruction);

            // 检查用户代币账户是否存在，如果不存在则创建
            var baseAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userBaseTokenAccount);

            if (!baseAccountInfo.WasSuccessful || baseAccountInfo.Result?.Value == null) {
                TransactionInstruction createBaseAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, baseMintKey);
                txBuilder.AddInstruction(createBaseAtaInstruction);
            }

            var quoteAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userQuoteTokenAccount);

            if (!quoteAccountInfo.WasSuccessful || quoteAccountInfo.Result?.Value == null) {
                TransactionInstruction createQuoteAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, quoteMintKey);
                txBuilder.AddInstruction(createQuoteAtaInstruction);
            }

            // 创建买入指令
            var instructionData = new List<byte>();
            instructionData.AddRange(BonkCpmmHelper.Instructions.swap_base_input);

            ulong amount_in = (ulong)maxSolCost;
            instructionData.AddRange(BitConverter.GetBytes(amount_in)); // cost max  sol
            ulong minimum_amount_out = tokenAmount;
            // ulong minimum_amount_A = ***********;
            instructionData.AddRange(BitConverter.GetBytes(minimum_amount_out)); // minimum_amount_out

            // 账户列表，按照IDL中的顺序
            var keys = new List<AccountMeta> {
                AccountMeta.Writable(account.PublicKey, true)
              , // user
                AccountMeta.ReadOnly(authority, false)
              , AccountMeta.ReadOnly(amm_config, false)
              , AccountMeta.Writable(poolData.pool, false)
              
              , AccountMeta.Writable(userQuoteTokenAccount, false)//input
              , AccountMeta.Writable(userBaseTokenAccount, false)//output
              
              , AccountMeta.Writable(wsol_vault, false)
              , AccountMeta.Writable(mint_vault, false)
                
              , AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)//9
              ,  AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)
              , AccountMeta.ReadOnly(quoteMintKey, false)
              , AccountMeta.ReadOnly(baseMintKey, false)
                
              , //13 quote_token_program
                AccountMeta.Writable(poolData.observation_key, false)
            };

            TransactionInstruction swapTranaction = new TransactionInstruction {
                Keys = keys, 
                ProgramId = BonkCpmmHelper.ProgramId, 
                Data = instructionData.ToArray()
            };
            Log.d($"{instructionData.ToArray().ToHex()}");
            TransactionInstruction transferSolInstruction = SystemProgram.Transfer(
                account.PublicKey,
                userQuoteTokenAccount,
                (ulong)maxSolCost);

            TransactionInstruction syncNativeInstruction = new TransactionInstruction {
                ProgramId = TokenProgram.ProgramIdKey
              , Keys = new List<AccountMeta> { AccountMeta.Writable(userQuoteTokenAccount, false) }
              , Data = new byte[] { 17 } // SyncNative instruction
            };
            // 关闭账户指令
            TransactionInstruction closeAccountInstruction = SolUtil.buildCloseAccountInstruction(userQuoteTokenAccount, account);

            txBuilder.AddInstruction(transferSolInstruction);
            txBuilder.AddInstruction(syncNativeInstruction);
            txBuilder.AddInstruction(swapTranaction);
            txBuilder.AddInstruction(closeAccountInstruction);

            // 构建并发送交易
            var tx = txBuilder.Build(account);
            var result = await Config.rpcClient.SendTransactionAsync(tx);

            if (result.WasSuccessful) {
                Log.d($"买入交易成功，交易哈希: {result.Result}");

                TransactionHistory.saveToDb(
                    walletPublicKey
                  , Config.TOKEN_SOL
                  , tokenMint
                  , solAmount: "-" + (baseSolCost / 1_000_000_000D)
                  , mintAmout: tokenAmount.ToString(), txHash: result.Result);
                return true;
            } else {
                Log.d($"买入交易失败: {result.Reason}");
                return false;
            }
        } catch (Exception ex) {
            Log.w($"买入执行失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> sell(string walletPublicKey
      , string privateKey
      , string mint
      , ulong tokenAmount
      , double tokenPrice
      , double slippagePercent = 5
      , double fee = 0
    ) {
        try {
            if (string.IsNullOrEmpty(walletPublicKey) ||
                string.IsNullOrEmpty(privateKey) ||
                string.IsNullOrEmpty(mint) ||
                tokenPrice < 0 ||
                slippagePercent < 0 ||
                fee < 0) {
                Log.w($"buy fail,参数错误,{walletPublicKey},{privateKey},{mint},{tokenPrice},{slippagePercent},{fee}");
                return false;
            }

            tokenAmount = tokenAmount * 1_000_000;

            BonkFunProgram.cachedPoolData.TryGetValue(mint, out var poolData);

            if (poolData == null) {
                Log.w($"获取poolData失败,{mint}");
                return false;
            }
            


            var wallet = new PublicKey(walletPublicKey);

            //内池交易参数,15个
            var payer = walletPublicKey; //1
            var authority = new PublicKey("GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL");
            var amm_config = poolData.amm_config;
            var poolState = poolData;

            var UserTokenPDA = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(wallet, new PublicKey(mint));
            var UserWSolnPDA = BonkBoundingCurveHelper.GetUserQuoteATA(wallet, new PublicKey(Config.TOKEN_WSOL));

            var mint_vault = poolData.token_1_vault; //8
            var wsol_vault = poolData.token_0_vault;
            var tokenMint = new PublicKey(mint);
            var tokenWSol = new PublicKey(Config.TOKEN_WSOL); //11


            tokenPrice = poolData.price();

            PublicKey mintKey = tokenMint;
            PublicKey wsolKey = tokenWSol;

            // 基础SOL成本（1 SOL = 1,000,000,000 lamports）
            double baseSolCost = tokenPrice * tokenAmount;

            Log.d($"买入:{tokenAmount}代币, SOL成本: {baseSolCost / 1_000_000_000.0:F9} SOL");

            // 计算包含滑点的最大SOL成本
            double maxSolCost = SolUtil.CalculateMinSolCostWithSlippage(baseSolCost, slippagePercent);
            // double maxSolCost = 1000000;
            Log.d($"最大SOL成本(含{slippagePercent}%滑点): {maxSolCost / 1_000_000_000.0:F9} SOL");

            // 设置最大交易计算单元
            uint units = 200_000;
            TransactionInstruction computeUnitLimitInstruction = ComputeBudgetHelper.SetComputeUnitLimit(units);

            // 创建优先交易费的指令
            double d = SolUtil.SolToLamp(fee);
            ulong priorityRate = (ulong)(d / units);
            TransactionInstruction priorityFeeInstruction = ComputeBudgetHelper.SetComputeUnitPrice(priorityRate);

            // 初始化钱包和客户端
            var account = WalletHelper.CreateAccountFromKeyPair(privateKey, walletPublicKey);
            // 获取用户代币账户 ,  72jyfCgTKZ7PbtkP1SHRyYDSauZohasUhBZhxYFwwyoZ
            PublicKey userMintTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, tokenMint);

            // 获取用户WSOL账户//AzcY5dwHdknr4LnWJPbzQ3xWQrWYaqikskVxD8X5xsYD
            PublicKey userWsolTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(account.PublicKey, tokenWSol);

            // 构建交易
            var lastBlockRsp = await Config.rpcClient.GetLatestBlockHashAsync();

            if (lastBlockRsp.WasSuccessful == false) {
                Log.w($"get last block hash failed");
                return false;
            }

            string recentBlockHash = lastBlockRsp.Result.Value.Blockhash;

            var txBuilder = new TransactionBuilder()
               .SetRecentBlockHash(recentBlockHash)
               .SetFeePayer(account)
               .AddInstruction(computeUnitLimitInstruction)
               .AddInstruction(priorityFeeInstruction);

            // 检查用户代币账户是否存在，如果不存在则创建
            var baseAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userMintTokenAccount);

            if (!baseAccountInfo.WasSuccessful || baseAccountInfo.Result?.Value == null) {
                TransactionInstruction createBaseAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, mintKey);
                txBuilder.AddInstruction(createBaseAtaInstruction);
            }

            var quoteAccountInfo = await Config.rpcClient.GetAccountInfoAsync(userWsolTokenAccount);

            if (!quoteAccountInfo.WasSuccessful || quoteAccountInfo.Result?.Value == null) {
                TransactionInstruction createQuoteAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                    account.PublicKey, account.PublicKey, wsolKey);
                txBuilder.AddInstruction(createQuoteAtaInstruction);
            }

            // 创建买入指令
            var instructionData = new List<byte>();
            instructionData.AddRange(BonkCpmmHelper.Instructions.swap_base_input);

            ulong minimum_amount_out = tokenAmount;
            instructionData.AddRange(BitConverter.GetBytes(minimum_amount_out)); // minimum_amount_out
            ulong amount_in = (ulong)maxSolCost;
            instructionData.AddRange(BitConverter.GetBytes(amount_in)); // cost max  sol

            // 账户列表，按照IDL中的顺序
            var keys = new List<AccountMeta> {
                AccountMeta.Writable(account.PublicKey, true)
              , // user
                AccountMeta.ReadOnly(authority, false)
              , AccountMeta.ReadOnly(amm_config, false)
              , AccountMeta.Writable(poolData.pool, false)
              
              , AccountMeta.Writable(userMintTokenAccount, false)//output
              , AccountMeta.Writable(userWsolTokenAccount, false)//input
              
              , AccountMeta.Writable(mint_vault, false)
              , AccountMeta.Writable(wsol_vault, false)
                
              , AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)//9
              ,  AccountMeta.ReadOnly(TokenProgram.ProgramIdKey, false)
              , AccountMeta.ReadOnly(mintKey, false)
              , AccountMeta.ReadOnly(wsolKey, false)
                
              , //13 quote_token_program
                AccountMeta.Writable(poolData.observation_key, false)
            };

            TransactionInstruction swapTransation = new TransactionInstruction {
                Keys = keys, ProgramId = BonkCpmmHelper.ProgramId, Data = instructionData.ToArray()
            };

            // 关闭账户指令
            TransactionInstruction closeAccountInstruction = SolUtil.buildCloseAccountInstruction(userWsolTokenAccount, account);

            txBuilder.AddInstruction(swapTransation);
            txBuilder.AddInstruction(closeAccountInstruction);

            // 构建并发送交易
            var tx = txBuilder.Build(account);
            var result = await Config.rpcClient.SendTransactionAsync(tx);

            if (result.WasSuccessful) {
                Log.d($"卖出交易成功，交易哈希: {result.Result}");

                TransactionHistory.saveToDb(
                    walletPublicKey
                  , Config.TOKEN_SOL
                  , tokenMint
                  , solAmount: (baseSolCost / 1_000_000_000D).ToString()
                  , mintAmout: "-" + tokenAmount.ToString(), txHash: result.Result);
                return true;
            } else {
                Log.d($"卖出交易失败: {result.Reason}");
                return false;
            }
        } catch (Exception ex) {
            Log.w($"卖出执行失败: {ex.Message}");
            return false;
        }
    }


    private static async Task<(ulong baseBalance, ulong quoteBalance)> GetPoolTokenBalances(PublicKey poolBaseTokenAccount, PublicKey poolQuoteTokenAccount) {
        ulong baseBalance = await SolUtil.GetTokenAccountBalance(poolBaseTokenAccount);
        ulong quoteBalance = await SolUtil.GetTokenAccountBalance(poolQuoteTokenAccount);
        
        Log.d($"池子代币余额: {baseBalance}, SOL余额: {quoteBalance}");
        return (baseBalance, quoteBalance);
    }

}
