using System;
using System.Collections.Generic;
using System.Linq;
// using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using ChainTool;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Solnet.Rpc;
using Solnet.Rpc.Core.Http;
using Solnet.Rpc.Models;
using Solnet.Rpc.Types;
using Solnet.Wallet;
using WebSocketSharp;
using Log = Shared.Log;

namespace Solnet.Programs.PumpFunAmm;

public class PumpfunAmmMigrationListener {
    private static JsonRpcClient jsonRpcClient = new JsonRpcClient();
    private readonly string _webSocketUrl;
    private WebSocket _webSocket;
    private Timer _heartbeatTimer; // 心跳定时器
    private static readonly int _heartbeatInterval = 30000; // 心跳间隔，30秒
    // private static readonly PublicKey _programId = new PublicKey("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
    private static readonly PublicKey _programId = PumpFunProgram.ProgramId;//监听内池
    // private readonly byte[] _migrationEventDiscriminator = new byte[] { 155,234,231,146,236,158,162,30 };////来源于pump-amm-idl.json
    // private readonly byte[] _createPoolEventDiscriminator = new byte[] { 177, 49, 12, 210, 160, 118, 167, 116 };//来源于pump-amm-idl.json
    private readonly byte[] _migrationEventDiscriminator = new byte[] { 155,234,231,146,236,158,162,30 };//来源于pump-idl.json
    private readonly byte[] _createPoolEventDiscriminator = new byte[] { 24,30,200,40,5,28,7,119}; //来源于pump-idl.json
    
        
        
    // 事件处理委托
    public delegate void PoolCreatedEventHandler(string poolAddress, string creator, string baseMint, string quoteMint);
    public delegate void MigrationCompletedEventHandler(string user, string mint, string mintAmount, string solAmount, string bondingCurve, string pool);
    
    // 事件
    public event PoolCreatedEventHandler OnPoolCreated;
    public event MigrationCompletedEventHandler OnMigrationCompleted;
    
    // 是否正在监听
    public bool IsListening { get; private set; }
    
    public string mintAddress{ get;  set; } ="";
    public string poolAddress{ get;  set; } ="";
    // 构造函数
    public PumpfunAmmMigrationListener(string webSocketUrl = null)
    {
        _webSocketUrl = webSocketUrl ?? Config.WS;
        IsListening = false;
    }
    
    // 开始监听
    public void StartListening()
    {
        try
        {
            if (IsListening)
            {
                Log.d("监听器已经在运行中");
                return;
            }
            
            _webSocket = new WebSocket(_webSocketUrl);
            
            _webSocket.OnOpen += (sender, e) =>
            {
                Log.d("WebSocket连接已打开");
                SubscribeToProgramLogs();
                StartHeartbeat(); // 启动心跳
                IsListening = true;
            };
            
            _webSocket.OnMessage += (sender, e) =>
            {
                if (e.Data != null)
                {
                    ProcessWebSocketMessage(e.Data);
                }
            };
            
            _webSocket.OnError += (sender, e) =>
            {
                Log.w($"WebSocket错误: {e.Message}");
            };
            
            _webSocket.OnClose += (sender, e) =>
            {
                Log.d($"WebSocket连接已关闭: {e.Code} - {e.Reason}");
                IsListening = false;
                StopHeartbeat(); // 停止心跳
                
                // 尝试重新连接
                if (e.Code != 1000) // 非正常关闭
                {
                    Task.Delay(5000).ContinueWith(_ => 
                    {
                        if (!IsListening) // 确保没有其他地方已经重新启动
                        {
                            StartListening();
                        }
                    });
                }
            };
            
            _webSocket.Connect();
        }
        catch (Exception ex)
        {
            Log.w($"启动WebSocket监听失败: {ex.Message}");
            IsListening = false;
        }
    }
    
    // 停止监听
    public void StopListening()
    {
        try
        {
            StopHeartbeat(); // 停止心跳
            
            if (_webSocket != null && _webSocket.IsAlive)
            {
                _webSocket.Close(1000, "正常关闭");
            }
            IsListening = false;
            Log.d("WebSocket监听已停止");
        }
        catch (Exception ex)
        {
            Log.w($"停止WebSocket监听失败: {ex.Message}");
        }
    }
    
    // 启动心跳
    private void StartHeartbeat()
    {
        try
        {
            // 停止可能存在的旧定时器
            StopHeartbeat();
            
            // 创建新的定时器，定期发送心跳
            _heartbeatTimer = new Timer(SendHeartbeat, null, _heartbeatInterval, _heartbeatInterval);
            Log.d($"WebSocket心跳已启动，间隔: {_heartbeatInterval}毫秒");
        }
        catch (Exception ex)
        {
            Log.w($"启动WebSocket心跳失败: {ex.Message}");
        }
    }

    // 停止心跳
    private void StopHeartbeat()
    {
        try
        {
            if (_heartbeatTimer != null)
            {
                _heartbeatTimer.Dispose();
                _heartbeatTimer = null;
                Log.d("WebSocket心跳已停止");
            }
        }
        catch (Exception ex)
        {
            Log.w($"停止WebSocket心跳失败: {ex.Message}");
        }
    }

    // 发送心跳
    private void SendHeartbeat(object state)
    {
        try
        {
            if (_webSocket != null && _webSocket.IsAlive)
            {
                // 发送ping请求
                var pingRequest = new
                {
                    jsonrpc = "2.0",
                    id = DateTime.Now.Ticks,
                    method = "ping"
                };
                
                string pingJson = JsonConvert.SerializeObject(pingRequest);
                _webSocket.Send(pingJson);
                // Log.d("已发送WebSocket心跳");
            }
            else
            {
                Log.w("WebSocket连接已断开，无法发送心跳");
                // 如果连接已断开，尝试重新连接
                if (!IsListening)
                {
                    StopHeartbeat();
                    Task.Delay(1000).ContinueWith(_ => StartListening());
                }
            }
        }
        catch (Exception ex)
        {
            Log.w($"发送WebSocket心跳失败: {ex.Message}");
            // 发生异常时，尝试重新连接
            if (_webSocket != null && !_webSocket.IsAlive && !IsListening)
            {
                StopHeartbeat();
                Task.Delay(1000).ContinueWith(_ => StartListening());
            }
        }
    }
    
    // 订阅程序日志
    private void SubscribeToProgramLogs()
    {
        try
        {
            var subscribeRequest = new
            {
                jsonrpc = "2.0",
                id = 1,
                method = "logsSubscribe",
                @params = new object[]
                {
                    new { mentions = new[] { _programId.ToString() } },
                    new { commitment = "confirmed" }
                }
            };
            
            string requestJson = JsonConvert.SerializeObject(subscribeRequest);
            _webSocket.Send(requestJson);
            Log.d($"已订阅程序日志: {_programId}");
        }
        catch (Exception ex)
        {
            Log.w($"订阅程序日志失败: {ex.Message}");
        }
    }
    
    // 处理WebSocket消息
    private void ProcessWebSocketMessage(string message)
    {
        try
        {
            var response = JObject.Parse(message);

            JToken method = response["method"];
            // 检查是否是订阅消息
            if (method?.ToString() == "logsNotification")
            {
                var result = response["params"]?["result"];
                if (result != null)
                {
                    ProcessLogNotification(result);
                }
            }
        }
        catch (Exception ex)
        {
            Log.w($"处理WebSocket消息失败: {ex.Message}");
        }
    }
    
    // 处理日志通知
    private void ProcessLogNotification(JToken result)
    {
        try
        {
            result = result["value"];

            if (result==null) {
                return;
            }
            // 获取交易签名
            string signature = result["signature"]?.ToString();
            if (string.IsNullOrEmpty(signature))
            {
                return;
            }
            
            // 获取日志
            var logs = result["logs"]?.ToObject<List<string>>();
            if (logs == null || logs.Count == 0)
            {
                return;
            }
            
            // 检查是否包含关注的事件
            bool hasCreatePoolEvent = false;
            bool hasMigrationEvent = false;
            
            foreach (var log in logs)
            {
                
                if (log.Contains("Instruction: Create"))
                {
                    hasCreatePoolEvent = true;
                } else if (log.Contains("Instruction: Migrate"))
                {
                    hasMigrationEvent = true;
                }
               
            }
            
            if (hasCreatePoolEvent )
            {
                // Log.d($"检测到事件:CreatePoolEvent，交易签名: {signature}");
                // Task.Run(() => GetTransactionDetails(signature, (TransactionMetaSlotInfo transaction) => {
                //     ParseCreatePoolEvent(transaction);
                //     return "";
                // }));
            }
            if (hasMigrationEvent) {
                Log.d($"检测到事件:CompletePumpAmmMigrationEvent，交易签名: {signature}");
                
                Task.Run(() => GetTransactionDetails(signature, (JsonRpcResult rst) => {
                    
                    TransactionDetail detail = JsonConvert.DeserializeObject<TransactionDetail>(rst.result.ToString() ?? string.Empty, JsonRpcClient._deserializerOptions);
                    (string mint, string pool) = parsePoolAddress(detail,signature);
                    if (!string.IsNullOrEmpty(pool)) {
                        Log.d($"amm pool:{pool}");
                    }
                }));
            }
        }
        catch (Exception ex)
        {
            Log.w($"处理日志通知失败: {ex.Message}");
        }
    }
    
    // 获取交易详情
    private async Task GetTransactionDetails(string signature, Action<JsonRpcResult> handRsp)
    {
        try
        {
            // 等待一段时间确保交易已确认
            await Task.Delay(2000);
            
            var rst = await jsonRpcClient.GetTransaction(Config.RPC, signature);
            if (rst == null||rst.result==null) {
                return;
            }
            // 解析交易数据
            if (handRsp != null) { handRsp.Invoke(rst); }
            
        }
        catch (Exception ex)
        {
            Log.w($"获取交易详情失败: {ex.Message}");
        }
    }

    
    private (string mint,string pool) parsePoolAddress(TransactionDetail detail,string signature) { 
        if (detail == null) {
            return new ValueTuple<string, string>(string.Empty, string.Empty);
        }
        
        string pool = "";
        string mint = "";
        try {
            // 查找mint==So11111111111111111111111111111111111111112的子项集合A
            var solTokenBalances = detail.meta.postTokenBalances
                .Where(x => x.mint == "So11111111111111111111111111111111111111112")
                .ToList();
            
            
            // 查找mint!=So11111111111111111111111111111111111111112的子项集合B
            var otherTokenBalances = detail.meta.postTokenBalances
                .Where(x => x.mint != "So11111111111111111111111111111111111111112")
                .ToList();
            foreach (PostTokenBalances otherTokenBalance in otherTokenBalances) {
                if(!string.IsNullOrWhiteSpace(mint)&&mint!=otherTokenBalance.mint) {
                    Log.w($"不应该存在多个代币,signature:{signature}");
                }
                mint = otherTokenBalance.mint;
            }
            pool = GetPoolForToken(mint);
            Log.d($"监听到pumpfun.amm外盘发射, mint地址:{mint}, pool:{pool}");
            if (OnMigrationCompleted!=null) {
                OnMigrationCompleted.Invoke(string.Empty, mint,"","", string.Empty, pool);
            }
            
            Log.d($"SOL代币账户数量: {solTokenBalances.Count}, 其他代币账户数量: {otherTokenBalances.Count}");
            
            // 查找A和B中owner相等的子项 - 使用AOT友好的方法
            var matchingPairs = new List<ValueTuple<PostTokenBalances, PostTokenBalances>>();
            foreach (var sol in solTokenBalances) {
                foreach (var other in otherTokenBalances) {
                    //并且是一个pda账号
                    if (sol.owner == other.owner&&!new PublicKey(sol.owner).IsOnCurve()) {
                        matchingPairs.Add(new ValueTuple<PostTokenBalances, PostTokenBalances>(sol, other));
                    }
                }
            }
            if (matchingPairs.Count<1) {
                Log.w($"解析交易详情失败: matchingPairs.Count<1");
            }
            if (matchingPairs.Count>1) {
                Log.w($"解析交易 matchingPairs.Count!=1");
                //按sol余额过滤
                matchingPairs = matchingPairs.FindAll(tuple => {
                    return tuple.Item1.uiTokenAmount.uiAmount>85D&&tuple.Item1.uiTokenAmount.uiAmount<86D;
                });

            }
            
            if (matchingPairs.Count!=1) {
                Log.w($"解析交易详情失败: matchingPairs.Count!=1");
                return new ValueTuple<string, string>(string.Empty, string.Empty);
            }
            var firstMatch = matchingPairs.First();
            
            Log.d($"解析得到的mint地址:{firstMatch.Item2.mint}, pool:{firstMatch.Item1.owner}");
            

           
        } catch (Exception e) {
            Log.d($"解析交易详情失败: {e.Message}, rst: {detail}");
        }
        
        return new ValueTuple<string, string>(mint, pool);
    }
    

    // 获取交易详情
    // 解析CreatePoolEvent
    private void ParseCreatePoolEvent(TransactionMetaInfo transaction)
    {
        try
        {
            // 获取交易中的账户列表
            var accountKeys = transaction.Transaction.Message.AccountKeys;
            
            // 获取交易中的指令数据
            var instructions = transaction.Transaction.Message.Instructions;
            
            foreach (var instruction in instructions)
            {
                // 检查是否是PumpFun AMM程序的指令
                if (instruction.ProgramIdIndex < accountKeys.Length && 
                    accountKeys[instruction.ProgramIdIndex] == _programId.ToString())
                {
                    // 解析指令数据
                    try {
                        byte[] data = Convert.FromBase64String(instruction.Data);

                        // 检查是否是CreatePool指令
                        if (data.Length >= 8 && IsCreatePoolInstruction(data)) {
                            // 从账户列表中提取池子地址和其他信息
                            if (instruction.Accounts.Length >= 5) {
                                string poolAddress = accountKeys[instruction.Accounts[0]];
                                string creator = accountKeys[instruction.Accounts[1]];
                                string baseMint = accountKeys[instruction.Accounts[3]];
                                string quoteMint = accountKeys[instruction.Accounts[4]];

                                Log.d($"新池子创建: {poolAddress}");
                                Log.d($"创建者: {creator}");
                                Log.d($"基础代币: {baseMint}");
                                Log.d($"报价代币: {quoteMint}");

                                // 触发事件
                                OnPoolCreated?.Invoke(poolAddress, creator, baseMint, quoteMint);
                            }
                        }
                    } catch (Exception e) { continue; }
                }
            }
        }
        catch (Exception ex)
        {
            Log.w($"解析CreatePoolEvent失败: {ex.Message}");
        }
    }
    
    
    // 检查是否是CreatePool指令
    private bool IsCreatePoolInstruction(byte[] data) {
        if (data.Length < 8) { return false; }

        // 检查指令标识符
        for (int i = 0; i < 8; i++) {
            if (data[i] != _createPoolEventDiscriminator[i]) { return false; }
        }

        return true;
    }
    
    // 监听特定代币的迁移事件
    public void MonitorTokenMigration(string tokenMint,string poolAddress)
    {
        this.mintAddress = tokenMint;
        this.poolAddress = poolAddress;
        Log.d($"当前监控的代币: {tokenMint} ,amm pool:{poolAddress}");
    }

    public static string GetPoolForToken(string tokenAddress) {
        PublicKey baseMintKey = new PublicKey(tokenAddress);
        PublicKey quoteMintKey = new PublicKey("So11111111111111111111111111111111111111112");
        // 方法1：使用完整的方法直接计算
        PublicKey pool = PumpfunAmmTransaction.GetCompletePoolPda(baseMintKey, quoteMintKey);
        // Console.WriteLine($"Pool PDA: {pool}");

        // 方法2：分步计算
        PublicKey poolAuthority = PumpfunAmmTransaction.GetPoolAuthorityPda(baseMintKey);
        PublicKey poolPda2 = PumpfunAmmTransaction.GetPoolPdaFromIdl(
            poolAuthority, 
            baseMintKey, 
            quoteMintKey, 
            PumpfunAmmTransaction.ProgramId
        );
        // Console.WriteLine($"Pool Authority: {poolAuthority}");
        // Console.WriteLine($"Pool PDA: {poolPda2}");
        return poolPda2;
    }
}
