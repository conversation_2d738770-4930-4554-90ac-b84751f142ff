using System;
using Newtonsoft.Json;

namespace Solnet.Programs;

/// <summary>
/// 自定义转换器，处理double类型的null值
/// </summary>
public class JsonNullDoubleValueConverter : JsonConverter {
    public override bool CanConvert(Type objectType) {
        // 处理double和double?类型
        return objectType == typeof(double) || objectType == typeof(double?);
    }


    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer) {
        // 如果JSON值为null
        if (reader.TokenType == JsonToken.Null) {
            // 如果是可空类型，返回null
            if (objectType == typeof(double?)) {
                return null;
            }
            // 如果是非可空类型，返回默认值0
            return 0.0;
        }
        
        // 如果是数字，直接转换
        if (reader.TokenType == JsonToken.Float || reader.TokenType == JsonToken.Integer) {
            return Convert.ToDouble(reader.Value);
        }
        
        // 如果是字符串，尝试解析
        if (reader.TokenType == JsonToken.String) {
            string stringValue = reader.Value.ToString();
            
            // 空字符串视为null
            if (string.IsNullOrEmpty(stringValue)) {
                return objectType == typeof(double?) ? (double?)null : 0.0;
            }
            
            // 尝试解析字符串为double
            if (double.TryParse(stringValue, out double result)) {
                return result;
            }
            
            // 解析失败返回默认值
            return objectType == typeof(double?) ? (double?)null : 0.0;
        }
        
        // 其他情况抛出异常
        throw new JsonSerializationException($"Unexpected token {reader.TokenType} when parsing double");
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer) {
        // 写入null或double值
        if (value == null) { writer.WriteNull(); } 
        else { writer.WriteValue((double)value); }
    }
}