using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Net;
using ChainTool.Models;
using ChainTool.ViewModels;
using Shared;
using Solnet.Rpc;

namespace ChainTool;

public class Config {
    public const string SOLANA_PROGRAM_ID_SYSTEM = "11111111111111111111111111111111";
    private static IRpcClient _rpcClient;

    public static string API_KEY
        = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************.1N8a2nYyMBzivmxv5WI7ec9Zap3l0F6fyDKg6FjdFe4";

    public static string RPC = "https://staked.helius-rpc.com?api-key=4ad36efe-8eb1-4b2d-86ea-9c09b9b2093e";
    public static string WS = "wss://mainnet.helius-rpc.com/?api-key=4ad36efe-8eb1-4b2d-86ea-9c09b9b2093e";

    public static IRpcClient rpcClient {
        get {
            if (_rpcClient == null) {
                // LoadSettings();
                _rpcClient = ClientFactory.GetClient(RPC);
            }

            return _rpcClient;
        }
    }

    //内盘创建者ID
    public static string Program_PumpFun = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
    public static string Program_PumpFun_AMM = "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA";
    public static string Program_SPL_TOKEN = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
    
    public static string Program_Raydium = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8";
    public static string Program_Jupiter = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4";
    public static string Program_Serum = "srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX"; //（已废弃）
    public static string Program_Orca = "9W959DqEETiGZocYWCQPaJ6sBmUzgfxXfqGeTEdp3aQP";
    public static string Program_OrcaWhirlpools = "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc";

    public static string TOKEN_SOL = "So11111111111111111111111111111111111111111"; //内盘主币
    public static string TOKEN_WSOL = "So11111111111111111111111111111111111111112"; //外盘主币
    public static string TOKEN_Mint = "5sqnhpeqsqFwLPSdC2EwzsPpdWWDer9Vabfx5JtHWA8r"; //代币
    public static string TOKEN_USDC = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; //代币

    public static string getPoolNameByProgram(string programName) {
        if (DexProgramList.TryGetValue(programName, out string poolName)) {
            return poolName;
        }
        return "unkonwn";
    }
    
    // 已知的DEX程序ID列表
    public static readonly Dictionary<string, string> DexProgramList = new Dictionary<string, string> {
        { Program_PumpFun, "Pump.fun" }
      , { Program_PumpFun_AMM, "Pump.fun.Amm" }
      , { Program_Raydium, "Raydium" }
      , { Program_Jupiter, "Jupiter" }
      , { Program_Serum, "Serum" }
      , { Program_Orca, "Orca" }
      , { Program_OrcaWhirlpools, "Orca Whirlpools" }
    };

    // 初始化配置
    static Config() { LoadSettings(); }

    // 加载设置
    public static void LoadSettings() {
        try {
            var settings = SettingsWindowViewModel.LoadSettingsStatic();
            RPC = settings.RpcUrl;
            WS = settings.WsUrl;
            API_KEY = settings.ApiKey;
            // 如果启用了代理，设置环境变量
            if (settings.EnableProxy) {
                SetSocks5EnvironmentVariables(settings);
            }else {
                ClearSocks5EnvironmentVariables();
            }
        } catch (Exception ex) {
            Log.d($"加载设置失败: {ex.Message}");
        }
    }


    // 设置SOCKS5代理环境变量
    public static void SetSocks5EnvironmentVariables(SettingsModel settings) {
        try {
            UpdateHttpClientProxy(settings);
        } catch (Exception ex) {
            Log.d($"设置SOCKS5代理环境变量失败: {ex.Message}");
        }
    }

    // 清除SOCKS5代理环境变量
    public static void ClearSocks5EnvironmentVariables() {
        return;
        try {
            UpdateHttpClientProxy(null);
        } catch (Exception ex) {
            Log.d($"清除SOCKS5代理环境变量失败: {ex.Message}");
        }
    }

    // 更新HttpClient代理设置
    public static void UpdateHttpClientProxy(SettingsModel settings) {
        if (settings != null && settings.EnableProxy && !string.IsNullOrWhiteSpace(settings.Host)) {
            string proxy = $"{settings.Host}:{settings.Socks5Port}";
            Environment.SetEnvironmentVariable("HTTP_PROXY", $"socks5://{proxy}");
            Environment.SetEnvironmentVariable("HTTPS_PROXY", $"socks5://{proxy}");
            Log.d($"已更新Environment代理设置: {proxy}");
        } else {
            Environment.SetEnvironmentVariable("HTTP_PROXY", null);
            Environment.SetEnvironmentVariable("HTTPS_PROXY", null);
            Log.d($"已清除Environment代理设置");
        }
    }

    // USDC代币Mint地址
    public static readonly string USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
}
