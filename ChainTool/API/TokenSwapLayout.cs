using System;
using System.IO;
using System.Text;

// solana web3.js

//
// TokenSwapLayoutLegacyV0:
// 适用于较旧版本的 Token Swap 程序，例如最早的 SPL Token Swap 合约。
// 如果合约部署时间较早（例如，2021 年之前），更可能是使用 V0 布局。

// TokenSwapLayoutV1:
// 适用于较新版本的 Token Swap 程序，支持更多功能和更复杂的费率计算。
// 如果合约部署时间较新（例如，2021 年之后），则更可能是使用 V1 布局。

// export const TokenSwapLayoutV1 = struct<RawTokenSwap>([
//     u8('version'),
//     u8('isInitialized'),
//     u8('bumpSeed'),
//     publicKey('poolTokenProgramId'),
//     publicKey('tokenAccountA'),
//     publicKey('tokenAccountB'),
//     publicKey('tokenPool'),
//     publicKey('mintA'),
//     publicKey('mintB'),
//     publicKey('feeAccount'),
//     u64('tradeFeeNumerator'),
//     u64('tradeFeeDenominator'),
//     u64('ownerTradeFeeNumerator'),
//     u64('ownerTradeFeeDenominator'),
//     u64('ownerWithdrawFeeNumerator'),
//     u64('ownerWithdrawFeeDenominator'),
//     u64('hostFeeNumerator'),
//     u64('hostFeeDenominator'),
//     u8('curveType'),
//     blob(32, 'curveParameters'),
// ]);

// export const TokenSwapLayoutLegacyV0 = struct([
//     u8('isInitialized'),
//     u8('nonce'),
//     publicKey('tokenAccountA'),
//     publicKey('tokenAccountB'),
//     publicKey('tokenPool'),
//     uint64('feesNumerator'),
//     uint64('feesDenominator'),
// ]);

//https://github.com/project-serum/serum-ts/blob/ec11b30fd373336806cd8e2df1873ac887c4e6e2/packages/spl-token-swap/src/instructions.ts#L83
public class TokenSwapLayout {
    public static TokenSwapLayoutV0 Decode(byte[] data) {
        if (data.Length >= 332) { return TokenSwapLayoutV1.Decode(data); }

        if (data.Length >= 114) { return TokenSwapLayoutV0.Decode(data); }

        return null;
    }
    public class TokenSwapLayoutV0 {
        // 定义 TokenSwapLayout 的字段
        public byte isInitialized { get; set; } //1
        public byte nonce { get; set; } //1
        public string TokenAccountA { get; set; } //32
        public string TokenAccountB { get; set; } //32
        public string tokenPool { get; set; } //32
        public ulong feesNumerator { get; set; } //8
        public ulong feeDenominator { get; set; } //8

        public static TokenSwapLayoutV0 Decode(byte[] data) {
            // 确保数据长度足够
            if (data.Length < 165) { throw new ArgumentException("数据长度不足，无法解析 TokenSwapLayout"); }

            // 创建一个 MemoryStream 和 BinaryReader 来解析二进制数据
            using (var stream = new MemoryStream(data))
            using (var reader = new BinaryReader(stream)) {
                // 初始化 TokenSwapLayout 对象
                var layout = new TokenSwapLayoutV0();

                // 逐字段解码
                layout.isInitialized = reader.ReadByte(); //1
                layout.nonce = reader.ReadByte(); //1
                layout.TokenAccountA = ReadPublicKey(reader); //32
                layout.TokenAccountB = ReadPublicKey(reader); //32
                layout.tokenPool = ReadPublicKey(reader); //32
                layout.feesNumerator = reader.ReadUInt64(); //8
                layout.feeDenominator = reader.ReadUInt64(); //8
                // 1+1+32*3+8*2= 114
                return layout;
            }
        }

        //PublicKey 接受一个 Base58 编码的字符串（Solana 地址）作为输入。
        private static string ReadPublicKey(BinaryReader reader) {
            // Solana 使用的公钥是基于 Ed25519 密钥对的。
            // 公钥的长度固定为 32字节。
            // 通常会使用 Base58 编码对公钥进行表示，这样的字符串长度通常为 44 个字符（取决于编码方式）。
            // Solana 的公钥是 32 字节长度
            var publicKeyBytes = reader.ReadBytes(32);
            return Base58.Encode(publicKeyBytes);
        }
        // private static string ReadPublicKey(BinaryReader reader) {
        //     // Solana 的公钥是 32 字节长度
        //     var publicKeyBytes = reader.ReadBytes(32);
        //     return BitConverter.ToString(publicKeyBytes).Replace("-", "").ToLower();
        // }
    }

    public class TokenSwapLayoutV1 : TokenSwapLayoutV0 {
        public byte Version { get; set; }
        public byte BumpSeed { get; set; }
        public string TokenProgramId { get; set; }
        public string mintA { get; set; }
        public string mintB { get; set; }
        public string feeAccount { get; set; }
        public ulong TradeFeeNumerator { get; set; }
        public ulong tradeFeeDenominator { get; set; }
        public ulong OwnerWithdrawFeeNumerator { get; set; }
        public ulong OwnerWithdrawFeeDenominator { get; set; }
        public ulong HostFeeNumerator { get; set; }
        public ulong HostFeeDenominator { get; set; }
        public ulong CurveType { get; set; }
        public byte[] curveParameters { get; set; } //32

        public static TokenSwapLayoutV1 Decode(byte[] data) {
            // 确保数据长度足够
            if (data.Length < 165) { throw new ArgumentException("数据长度不足，无法解析 TokenSwapLayout"); }

            // 创建一个 MemoryStream 和 BinaryReader 来解析二进制数据
            using (var stream = new MemoryStream(data))
            using (var reader = new BinaryReader(stream)) {
                // 初始化 TokenSwapLayout 对象
                var layout = new TokenSwapLayoutV1();

                // 逐字段解码
                layout.Version = reader.ReadByte(); //1
                layout.isInitialized = reader.ReadByte(); //1
                layout.BumpSeed = reader.ReadByte(); //1
                layout.TokenProgramId = ReadPublicKey(reader); //32
                layout.TokenAccountA = ReadPublicKey(reader); //32
                layout.TokenAccountB = ReadPublicKey(reader); //32
                layout.tokenPool = ReadPublicKey(reader); //32
                layout.mintA = ReadPublicKey(reader); //32
                layout.mintB = ReadPublicKey(reader); //32
                layout.feeAccount = ReadPublicKey(reader); //32
                layout.tradeFeeDenominator = reader.ReadUInt64(); //8
                layout.feesNumerator = reader.ReadUInt64(); //8
                layout.OwnerWithdrawFeeDenominator = reader.ReadUInt64(); //8
                layout.OwnerWithdrawFeeNumerator = reader.ReadUInt64(); //8
                layout.OwnerWithdrawFeeDenominator = reader.ReadUInt64(); //8
                layout.HostFeeNumerator = reader.ReadUInt64(); //8
                layout.HostFeeDenominator = reader.ReadUInt64(); //8
                layout.CurveType = reader.ReadByte(); //1
                layout.curveParameters = reader.ReadBytes(32); //32
                // 3+32*7+8*7+8*2+1+32 = 332
                return layout;
            }
        }

        //PublicKey 接受一个 Base58 编码的字符串（Solana 地址）作为输入。
        private static string ReadPublicKey(BinaryReader reader) {
            // Solana 使用的公钥是基于 Ed25519 密钥对的。
            // 公钥的长度固定为 32字节。
            // 通常会使用 Base58 编码对公钥进行表示，这样的字符串长度通常为 44 个字符（取决于编码方式）。
            // Solana 的公钥是 32 字节长度
            var publicKeyBytes = reader.ReadBytes(32);
            return Base58.Encode(publicKeyBytes);
        }
        // private static string ReadPublicKey(BinaryReader reader) {
        //     // Solana 的公钥是 32 字节长度
        //     var publicKeyBytes = reader.ReadBytes(32);
        //     return BitConverter.ToString(publicKeyBytes).Replace("-", "").ToLower();
        // }

    }

    
}
