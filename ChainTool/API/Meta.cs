using System;
using System.Text;
using System.Text.Json.Serialization;
using System.Linq;
using Solnet.Rpc;
using Solnet.Wallet;
using Solnet.Programs;
using Solnet.Programs.TokenSwap;
using Solnet.Programs.Utilities;
using System.Security.Cryptography;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Solnet.Metaplex.NFT.Library;
using Solnet.Metaplex.Utilities;
using Solnet.Rpc.Core.Http;
using Solnet.Rpc.Messages;
using Solnet.Rpc.Models;

namespace ChainTool;

public class Meta {
    
    //获取token的meta数据结构 和 owner Account
    public static  MetaDataParser getTokenMetadataAccount(IRpcClient rpcClient,string tokenAddress) {
        // Meta.getTokenMeta(rpcClient, tokenPublicKey,MetaProgram);
        MetaDataParser metaAccount  = Meta.get(rpcClient, tokenAddress);
        if (metaAccount == null)return null;
        Console.WriteLine($"meta  symbol {metaAccount.metadata.symbol}, owner program:{metaAccount.owner}");

        if (metaAccount.metadata.creators != null) {
            foreach (Solnet.Metaplex.NFT.Library.Creator metadataCreator in metaAccount.metadata.creators) {
                Console.WriteLine($"   creator , key:{metadataCreator.key}");
            }
        }
        return metaAccount;
    }
    
    public static async Task< MetaDataParser> getTokenMetadataAccountAsync(IRpcClient rpcClient,string tokenAddress) {
        // Meta.getTokenMeta(rpcClient, tokenPublicKey,MetaProgram);
        MetaDataParser metaAccount  = await Meta.getAsync(rpcClient, tokenAddress);
        if (metaAccount == null)return null;
        Console.WriteLine($"meta  symbol {metaAccount.metadata.symbol}, owner program:{metaAccount.owner}");

        if (metaAccount.metadata.creators != null) {
            foreach (Solnet.Metaplex.NFT.Library.Creator metadataCreator in metaAccount.metadata.creators) {
                Console.WriteLine($"   creator , key:{metadataCreator.key}");
            }
        }
        return metaAccount;
    }
    
    //solnet官方实现(异步)
    private static async Task<MetaDataParser> getAsync(IRpcClient rpcClient, string mintAddress) {
        var tokenAddress = new PublicKey(mintAddress);
        PublicKey metadataPDA = PDALookup.FindMetadataPDA(tokenAddress);
        Console.WriteLine($"metadata PDA:{metadataPDA}");
        RequestResult<ResponseValue<AccountInfo>> accountInfoAsync =  await rpcClient.GetAccountInfoAsync(metadataPDA);
        if (!accountInfoAsync.WasSuccessful)
            return null;
        
        AccountInfo accInfo = accountInfoAsync?.Result?.Value??null;
        if (accInfo==null) {
            return null;
        }
        //metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s
        Console.WriteLine($"metadata PDA  program owner is :{accInfo.Owner} , oh~~ it is a fixed address");
        if (accInfo.Owner.Contains("meta")&&accInfo.Data!=null&&accInfo.Data.Count>=2) {
            Console.WriteLine($"get meta account dataLen : {accInfo.Data[0].Length}" );
            Console.WriteLine($"parse meta data and fetch offchain metadata..." );
            return MetaDataParser.Parse(accInfo);//解析meta数据和获取链外的网络数据
        }
        return null;
    }
    //solnet官方实现,计算PDA后调用RPC获取Meta账号
    public static MetaDataParser get(IRpcClient rpcClient, string mintAddress) {
        var tokenAddress = new PublicKey(mintAddress);
        PublicKey metadataPDA = PDALookup.FindMetadataPDA(tokenAddress);
        Console.WriteLine($"metadata PDA:{metadataPDA}");
        RequestResult<ResponseValue<AccountInfo>> accountInfoAsync =  rpcClient.GetAccountInfo(metadataPDA);
        if (!accountInfoAsync.WasSuccessful)
            return null;
        
        AccountInfo accInfo = accountInfoAsync?.Result?.Value??null;
        if (accInfo==null) {
            return null;
        }
        //metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s
        Console.WriteLine($"metadata PDA  program owner is :{accInfo.Owner} , oh~~ it is a fixed address");
        if (accInfo.Owner.Contains("meta")&&accInfo.Data!=null&&accInfo.Data.Count>=2) {
            Console.WriteLine($"get meta account dataLen : {accInfo.Data[0].Length}" );
            Console.WriteLine($"parse meta data and fetch offchain metadata..." );
            return MetaDataParser.Parse(accInfo);//解析meta数据和获取链外的网络数据
        }
        return null;
    }
    //仿照rust代码实现
    public static void getTokenMeta(IRpcClient rpcClient, string mintAddress,string tokenMetadataProgramId) {
        // Step 1: 定义 Token Metadata Program ID
        var tokenMetadataProgramIdKey = new PublicKey(tokenMetadataProgramId);
        // Step 2: 定义 Token 的 Mint Address (替换为你的实际 Mint Address)

        // Step 3: 计算 PDA
        PublicKey metadataAddress;
        byte bump;
        bool success = genPDA(mintAddress, tokenMetadataProgramIdKey, out metadataAddress, out bump);

        if (!success) {
            Console.WriteLine("无法计算 PDA，可能种子无效或地址不合法。");
            return;
        }

        Console.WriteLine($"token:{mintAddress}");
        Console.WriteLine($"Metadata PDA: {metadataAddress}");
        Console.WriteLine($"Bump: {bump}");

        // Step 4: 使用 RPC 获取 Metadata 数据
        var metadataAccount = rpcClient.GetAccountInfo(metadataAddress);

        if (metadataAccount.WasSuccessful) {
            Console.WriteLine("Metadata Account Info:");
            Console.WriteLine(metadataAccount.Result.Value.Data[0]); // Solana 的数据是 Base64 编码的
            
            string base64Data = metadataAccount.Result.Value.Data[0]; // 
            byte[] rawData = Convert.FromBase64String(base64Data);

            //解码
            var metadata = MetadataDecoder.Decode(rawData);

            Console.WriteLine($"Key: {metadata.Key}");
            Console.WriteLine($"Update Authority: {metadata.UpdateAuthority}");
            Console.WriteLine($"Mint: {metadata.Mint}");
            Console.WriteLine($"Name: {metadata.Data.Name}");
            Console.WriteLine($"Symbol: {metadata.Data.Symbol}");
            Console.WriteLine($"URI: {metadata.Data.Uri}");
            Console.WriteLine($"Data.SellerFeeBasisPoints: {metadata.Data.SellerFeeBasisPoints}");
            Console.WriteLine($"Data.creatorCount: {metadata.Data.HasCreator}");
            if (metadata.Data.HasCreator)
            {
                Console.WriteLine("Creators:");
                foreach (var creator in metadata.Data.Creators)
                {
                    Console.WriteLine($"  Address: {creator.Address}");
                    Console.WriteLine($"  Verified: {creator.Verified}");
                    Console.WriteLine($"  Share: {creator.Share}");
                }
            }
            Console.WriteLine($"Primary Sale Happened: {metadata.PrimarySaleHappened}");
            Console.WriteLine($"Is Mutable: {metadata.IsMutable}");
            Console.WriteLine($"editionNonce: {metadata.editionNonce}");
            Console.WriteLine($"tokenStandard: {metadata.tokenStandard}");
        } else {
            Console.WriteLine($"无法获取 Metadata 数据: {metadataAccount.Reason}");
        }
    }

    public static bool genPDA(string mintAddress, PublicKey tokenMetadataProgramId, out PublicKey metadataAddress, out byte bump) {
        return PublicKey.TryFindProgramAddress(
            new List<byte[]> {
                System.Text.Encoding.UTF8.GetBytes("metadata")
              , // 固定字符串
                tokenMetadataProgramId.KeyBytes
              , // Token Metadata Program ID
                new PublicKey(mintAddress) // Token 的 Mint Address
            },
            tokenMetadataProgramId,
            out metadataAddress,
            out bump);
    }
    public static bool genGlobalPDA(PublicKey tokenMetadataProgramId, out PublicKey PDA, out byte bump) {
        return PublicKey.TryFindProgramAddress(
            new List<byte[]> {
                Encoding.UTF8.GetBytes("global-state")
            },
            tokenMetadataProgramId,
            out PDA,
            out bump);
    }

// #[repr(C)]
// #[cfg_attr(feature = "serde-feature", derive(Serialize, Deserialize))]
// #[derive(Clone, BorshSerialize, Debug, PartialEq, Eq, ShankAccount)]
//     pub struct Metadata {
//         /// Account discriminator.
//         pub key: Key,
//         /// Address of the update authority.
//     #[cfg_attr(feature = "serde-feature", serde(with = "As::<DisplayFromStr>"))]
//         pub update_authority: Pubkey,
//         /// Address of the mint.
//     #[cfg_attr(feature = "serde-feature", serde(with = "As::<DisplayFromStr>"))]
//         pub mint: Pubkey,
//         /// Asset data.
//         pub data: Data,
//         // Immutable, once flipped, all sales of this metadata are considered secondary.
//         pub primary_sale_happened: bool,
//         // Whether or not the data struct is mutable, default is not
//         pub is_mutable: bool,
//         /// nonce for easy calculation of editions, if present
//         pub edition_nonce: Option<u8>,
//         /// Since we cannot easily change Metadata, we add the new DataV2 fields here at the end.
//         pub token_standard: Option<TokenStandard>,
//         /// Collection
//         pub collection: Option<Collection>,
//         /// Uses
//         pub uses: Option<Uses>,
//         /// Collection Details
//         pub collection_details: Option<CollectionDetails>,
//         /// Programmable Config
//         pub programmable_config: Option<ProgrammableConfig>,
//     }

// #[repr(C)]
// #[cfg_attr(feature = "serde-feature", derive(Serialize, Deserialize))]
// #[derive(BorshSerialize, BorshDeserialize, Default, PartialEq, Eq, Debug, Clone)]
// pub struct Data {
//     /// The name of the asset
//     pub name: String,
//     /// The symbol for the asset
//     pub symbol: String,
//     /// URI pointing to JSON representing the asset
//     pub uri: String,
//     /// Royalty basis points that goes to creators in secondary sales (0-10000)
//     pub seller_fee_basis_points: u16,
//     /// Array of creators, optional
//     pub creators: Option<Vec<Creator>>,
// }
//
// #[repr(C)]
// #[cfg_attr(feature = "serde-feature", derive(Serialize, Deserialize))]
// #[derive(BorshSerialize, BorshDeserialize, PartialEq, Eq, Debug, Clone)]
// pub struct DataV2 {
//     /// The name of the asset
//     pub name: String,
//     /// The symbol for the asset
//     pub symbol: String,
//     /// URI pointing to JSON representing the asset
//     pub uri: String,
//     /// Royalty basis points that goes to creators in secondary sales (0-10000)
//     pub seller_fee_basis_points: u16,
//     /// Array of creators, optional
//     pub creators: Option<Vec<Creator>>,
//     /// Collection
//     pub collection: Option<Collection>,
//     /// Uses
//     pub uses: Option<Uses>,
// }


// #[derive(BorshSerialize, BorshDeserialize, Clone, Debug, Eq, PartialEq)]
// #[cfg_attr(feature = "serde", derive(serde::Serialize, serde::Deserialize))]
// pub struct Creator {
// #[cfg_attr(
//     feature = "serde",
//     serde(with = "serde_with::As::<serde_with::DisplayFromStr>")
//         )]
//     pub address: Pubkey,
//     pub verified: bool,
//     pub share: u8,
// }
//https://github.com/metaplex-foundation/mpl-token-metadata/blob/main/programs/token-metadata/program/src/state/metadata.rs
     public class Metadata
    {
        public MetadataDecoder.Key Key { get; set; }
        public string UpdateAuthority { get; set; }
        public string Mint { get; set; }
        public MetadataData Data { get; set; }
        public bool PrimarySaleHappened { get; set; }
        public bool IsMutable { get; set; }
        public byte editionNonce { get; set; }
        public MetadataDecoder.TokenStandard tokenStandard { get; set; }//大于1,则有拓展数据
        public byte[] collection { get; set; }//34
        public byte[] uses { get; set; }//42
       
    }

    public class MetadataData
    {
        public string Name { get; set; }
        public string Symbol { get; set; }
        public string Uri { get; set; }
        public ushort SellerFeeBasisPoints { get; set; }
        public bool HasCreator { get; set; }
        public List<Creator> Creators { get; set; }
        
        public int GetSerializedLength()
        {
            int length = 0;
            length += Name.Length + 4; // Rust字符串前缀长度 + 内容
            length += Symbol.Length + 4;
            length += Uri.Length + 4;
            length += 2; // SellerFeeBasisPoints
            if (Creators != null)
            {
                length += 4; // Creators数量
                length += Creators.Count * Creator.SerializedLength;
            }
            return length;
        }
    }

    public class Creator
    {
        public string Address { get; set; }
        public bool Verified { get; set; }
        public byte Share { get; set; }
        
        public const int SerializedLength = 32 + 1 + 1; // PublicKey + Verified + Share

    }

    public class MetadataDecoder
    {
        public static Metadata Decode(byte[] data)
        {
            using (var reader = new BinaryReader(new MemoryStream(data)))
            {
                var metadata = new Metadata();

                // Key (1 byte)
                metadata.Key = (Key)reader.ReadByte();

                // Update Authority (32 bytes - PublicKey)
                metadata.UpdateAuthority = ReadPublicKey(reader);

                // Mint (32 bytes - PublicKey)
                metadata.Mint = ReadPublicKey(reader);

                // Data (Variable length)
                metadata.Data = ReadData(reader);

                // Primary Sale Happened (1 byte - boolean)
                metadata.PrimarySaleHappened = reader.ReadByte() != 0;

                // Is Mutable (1 byte - boolean)
                metadata.IsMutable = reader.ReadByte() != 0;
                
                //可选的拓展数据 editionNonce
                var isHasEditionNonce = reader.ReadByte() != 0;
                if (isHasEditionNonce) {
                    metadata.editionNonce = reader.ReadByte();
                }
                //可选的拓展数据 tokenStandard
                var hasTokenStandard = reader.ReadByte() != 0;

                if (hasTokenStandard) {
                    metadata.tokenStandard = (TokenStandard)reader.ReadByte();
                }
                
                return metadata;
            }
        }

        private static MetadataData ReadData(BinaryReader reader)
        {
            var data = new MetadataData();

            // Name (string with prefix length)
            data.Name = ReadRustString(reader);

            // Symbol (string with prefix length)
            data.Symbol = ReadRustString(reader);

            // URI (string with prefix length)
            data.Uri = ReadRustString(reader);

            // Seller Fee Basis Points (2 bytes - ushort)
            data.SellerFeeBasisPoints = reader.ReadUInt16();
            
            // Creators Length (4 bytes - uint32)
            data.HasCreator = reader.ReadByte()>0 ;

            if (data.HasCreator)
            {
                data.Creators = new List<Creator>();
                uint length = reader.ReadUInt32();
                for (int i = 0; i < length; i++)
                {
                    var creator = new Creator
                    {
                        // Address (32 bytes - PublicKey)
                        Address = ReadPublicKey(reader),
                        // Verified (1 byte - boolean)
                        Verified = reader.ReadByte() != 0,
                        // Share (1 bytes - ushort)
                        Share =reader.ReadByte(),
                    };
                    data.Creators.Add(creator);
                }
            }

            return data;
        }
        public enum TokenStandard:byte {
            NonFungible,
            FungibleAsset,
            Fungible,
            NonFungibleEdition,
            ProgrammableNonFungible,
            ProgrammableNonFungibleEdition,
        }
        public enum Key:byte {
            Uninitialized,
            EditionV1,
            MasterEditionV1,
            ReservationListV1,
            MetadataV1,
            ReservationListV2,
            MasterEditionV2,
            EditionMarker,
            UseAuthorityRecord,
            CollectionAuthorityRecord,
            TokenOwnedEscrow,
            TokenRecord,
            MetadataDelegate,
            EditionMarkerV2,
            HolderDelegate,
        }
        private static string ReadRustString(BinaryReader reader)
        {
            // Rust string format: [length (4 bytes)][UTF-8 encoded string]
            int length = reader.ReadInt32();
            return Encoding.UTF8.GetString(reader.ReadBytes(length));
        }

        private static string ReadPublicKey(BinaryReader reader)
        {
            // PublicKey is 32 bytes
            var keyBytes = reader.ReadBytes(32);
            return System.Base58.Encode(keyBytes);
        }
    }
}
