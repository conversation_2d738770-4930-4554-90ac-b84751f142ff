// Decompiled with JetBrains decompiler
// Type: Solnet.Metaplex.NFT.Library.MetadataAccount
// Assembly: Solnet.Metaplex, Version=8.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: F5BA2AAC-B929-4F3D-8A41-A9981B50DB01
// Assembly location: C:\Users\<USER>\.nuget\packages\solana.metaplex\8.0.0\lib\net8.0\Solnet.Metaplex.dll
// XML documentation location: C:\Users\<USER>\.nuget\packages\solana.metaplex\8.0.0\lib\net8.0\Solnet.Metaplex.xml

using Newtonsoft.Json;
using Solnet.Metaplex.Utilities;
using Solnet.Metaplex.Utilities.Json;
using Solnet.Programs.Utilities;
using Solnet.Rpc;
using Solnet.Rpc.Core.Http;
using Solnet.Rpc.Messages;
using Solnet.Rpc.Models;
using Solnet.Wallet;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Numerics;
using System.Threading.Tasks;
using Solnet.Metaplex.NFT.Library;
using Collection = Solnet.Metaplex.NFT.Library.Collection;
using Creator = Solnet.Metaplex.NFT.Library.Creator;

#nullable enable
namespace ChainTool
{
  /// <summary> Metaplex Metadata Account Class </summary>
  public class MetaDataParser
  {
    /// <summary> metadata public key </summary>
    public 
    #nullable disable
    PublicKey metadataKey;
    /// <summary> update authority key </summary>
    public PublicKey updateAuthority;
    /// <summary> mint public key </summary>
    public string mint;
    /// <summary> data struct </summary>
    public OnChainData metadata;
    /// <summary> Off Chain Metadata </summary>
    public string uri;
    /// <summary> standard Solana account info </summary>
    public AccountInfo accInfo;
    /// <summary> owner, should be Metadata program</summary>
    public PublicKey owner;

    /// <summary> Constructor </summary>
    /// <param name="accInfo"> Soloana account info </param>
    public static MetaDataParser Parse(AccountInfo accInfo) {
        MetaDataParser meta = new MetaDataParser();

        try {
            meta.metadata = MetadataAccount.ParseData(accInfo.Data);
            meta.uri = meta.metadata.uri;
            meta.accInfo = accInfo;
            byte[] array = Convert.FromBase64String(accInfo.Data[0]);
            Span<byte> key1 = array.AsSpan<byte>(1, 32);
            Span<byte> key2 = array.AsSpan<byte>(33, 32);
            meta.owner = new PublicKey(accInfo.Owner);
            meta.updateAuthority = new PublicKey((ReadOnlySpan<byte>)key1);
            meta.mint = (string)new PublicKey((ReadOnlySpan<byte>)key2);
        } catch (Exception ex) { Console.WriteLine((object)ex); }

        return meta;
    }


    /// <summary> Parse raw data used to propagate the metadata account class</summary>
    /// <param name="data"> data </param>
    /// <returns> data struct </returns>
    /// <remarks> parses an array of bytes into a data struct </remarks>
    private static OnChainData ParseData(List<string> data)
    {
      try
      {
        ReadOnlySpan<byte> data1 = new ReadOnlySpan<byte>(Convert.FromBase64String(data[0]));
        string result1;
        data1.GetBorshString(65, out result1);
        string result2;
        data1.GetBorshString(101, out result2);
        string result3;
        data1.GetBorshString(115, out result3);
        uint u16 = (uint) data1.GetU16(319);
        bool flag1 = data1.GetBool(321);
        byte u8_1 = data1.GetU8(322);
        IList<Creator> _creators = (IList<Creator>) null;
        Uses useInfo = (Uses) null;
        Collection _collection = (Collection) null;
        ProgrammableConfig programmableconfig = (ProgrammableConfig) null;
        int num1 = 0;
        if (data1.Length < 326 + (int) u8_1 * 34)
          flag1 = false;
        int offset1;
        if (flag1)
        {
          _creators = DecodeCreators(data1.GetSpan(326, (int) u8_1 * 34));
          offset1 = 326 + (int) u8_1 * 34;
        }
        else
          offset1 = 321 + 1;
        data1.GetBool(offset1);
        int offset2 = offset1 + 1;
        bool _isMutable = data1.GetBool(offset2);
        int offset3 = offset2 + 1 + 1;
        byte u8_2 = data1.GetU8(offset3);
        int offset4 = offset3 + 1 + 1;
        byte u8_3 = data1.GetU8(offset4);
        int offset5 = offset4 + 1;
        if (offset5 + 8 <= data1.Length)
        {
          int num2 = data1.GetBool(offset5) ? 1 : 0;
          int offset6 = offset5 + 1;
          if (num2 != 0)
          {
            bool flag2 = data1.GetBool(offset6);
            int offset7 = offset6 + 1;
            PublicKey pubKey = data1.GetPubKey(offset7);
            offset6 = offset7 + 32;
            int num3 = flag2 ? 1 : 0;
            _collection = new Collection(pubKey, num3 != 0);
          }
          int num4 = data1.GetBool(offset6) ? 1 : 0;
          int offset8 = offset6 + 1;
          if (num4 != 0)
          {
            int _useMethod = (int) data1.GetBytes(offset8, 1)[0];
            int offset9 = offset8 + 1;
            string str1 = data1.GetU64(offset9).ToString("x");
            int offset10 = offset9 + 8;
            string str2 = data1.GetU64(offset10).ToString("x");
            offset8 = offset10 + 8 + 1;
            int int32_1 = Convert.ToInt32(str1);
            int int32_2 = Convert.ToInt32(str2);
            useInfo = new Uses((UseMethod) _useMethod, int32_1, int32_2);
          }
          if (offset8 + 1 <= data1.Length)
          {
            int num5 = data1.GetBool(offset8) ? 1 : 0;
            ++offset8;
            if (num5 != 0)
            {
              int num6 = (int) data1.GetBytes(offset8, 1)[0];
              int offset11 = offset8 + 1;
              BigInteger u64 = (BigInteger) data1.GetU64(offset11);
              offset8 = offset11 + 8;
            }
          }
          if (offset8 + 1 <= data1.Length)
          {
            int num7 = data1.GetBool(offset8) ? 1 : 0;
            int offset12 = offset8 + 1;
            if (num7 != 0)
            {
              int num8 = (int) data1.GetBytes(offset12, 1)[0];
              int offset13 = offset12 + 1;
              if (num8 == 0)
              {
                int num9 = data1.GetBool(offset13) ? 1 : 0;
                int offset14 = offset13 + 1;
                if (num9 != 0)
                {
                  PublicKey pubKey = data1.GetPubKey(offset14);
                  num1 = offset14 + 32;
                  programmableconfig = new ProgrammableConfig(pubKey);
                }
              }
            }
          }
        }
        return new OnChainData(result1.TrimEnd(char.MinValue), result2.TrimEnd(char.MinValue), result3.TrimEnd(char.MinValue), u16, _creators, (int) u8_2, (int) u8_3, _collection, useInfo, programmableconfig, _isMutable);
      }
      catch (Exception ex)
      {
        throw new Exception("could not decode account data from base64", ex);
      }
    }

    /// <summary>GetAccount Method Retrieves the metadata of a token including both onchain and offchain data</summary>
    /// <param name="client"> solana rpcclient </param>
    /// <param name="tokenAddress"> public key of a account to parse </param>
    /// <returns> Metadata account </returns>
    /// <remarks> it will try to find a metadata even from a token associated account </remarks>
    public static async Task<MetadataAccount> GetAccount(IRpcClient client, PublicKey tokenAddress)
    {
      RequestResult<ResponseValue<AccountInfo>> accountInfoAsync = await client.GetAccountInfoAsync(tokenAddress.Key);
      if (!accountInfoAsync.WasSuccessful)
        return (MetadataAccount) null;
      AccountInfo accInfo = accountInfoAsync.Result.Value;
      if (accInfo.Owner.Contains("meta"))
        return new MetadataAccount(accInfo);
      byte[] array = Convert.FromBase64String(accInfo.Data[0]);
      return await MetadataAccount.GetAccount(client, PDALookup.FindMetadataPDA(array.Length != 165 ? tokenAddress : new PublicKey(array.AsSpan<byte>(0, 32).ToArray())));
    }
    
    static IList<Creator> DecodeCreators(ReadOnlySpan<byte> creatorsVector)
    {
        List<Creator> creatorList = new List<Creator>();
        int num = creatorsVector.Length / Creator.length;
        int offset = 0;
        for (int index = 0; index < num; ++index)
        {
            Creator creator = new Creator(creatorsVector.GetSpan(offset, Creator.length));
            offset += Creator.length;
            creatorList.Add(creator);
        }
        return (IList<Creator>) creatorList;
    }

  }
}
