using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using ChainTool.Models;
using Shared;
using Solnet.Programs;
using Solnet.Programs.Models.TokenProgram;
using Solnet.Programs.PumpFunAmm;
using Solnet.Rpc.Core.Http;
using Solnet.Rpc.Messages;
using Solnet.Wallet;

namespace ChainTool;

public class API {
    // 根据token获取market列表
    public static Task<AmmMarketPair> TOKEN_MARKETS(string mint) {
        string  ammPool= PumpfunAmmMigrationListener.GetPoolForToken(mint);
        string innerPool = PumpFunProgram.GetBondingCurvePda(new PublicKey(mint)).Key;
        var innerMarket  = new AmmMarket() {
            pool = innerPool,
            programID = PumpFunProgram.ProgramId.Key,
            programName = Config.getPoolNameByProgram(PumpFunProgram.ProgramId.Key),
            mint = mint,
        };
        var ammMarket  = new AmmMarket() {
            pool = ammPool,
            programID = PumpfunAmmTransaction.ProgramId.Key,
            programName = Config.getPoolNameByProgram(PumpfunAmmTransaction.ProgramId.Key),
            mint = mint,
        };
        var market = new AmmMarketPair() {
           innerMarket = innerMarket,
           ammMarket = ammMarket,
           mint = mint,
        };
        return Task.FromResult(market);
    }

    // ### 查询单个池子／市场详情,传入池子，返回该池子的价格,代币和sol数量
    public static async Task<AmmMarket> MARKETS_DETAILED(AmmMarket market) {
        
        if (market.programName == "FlipFlopAmm") {
            var bonk  = await FilpFlopCpmmHelper.GetPoolData(market.pool);
            market.tokenAmount = (double)bonk.tokenAmount / 1_000_000 + "";
            market.solAmount = (double)bonk.solAmount / 1_000_000_000 + "";
            market.price = $"{bonk.price():F9}";
            return market;
        }
        
        if (market.programID == PumpFunProgram.ProgramId.Key) {
            var bondingCurveHelper = new PumpFunBondingCurveHelper(Config.rpcClient);
            var bondingCurve = await bondingCurveHelper.GetBondingCurveDataAsync(new PublicKey(market.mint));

            if (bondingCurve == null) {
                Log.w("无法获取bonding curve数据，无法计算准确价格");
                return market;
            }
            market.tokenAmount = (double)bondingCurve.RealTokenReserves / 1_000_000 + "";
            market.solAmount = (double)bondingCurve.RealSolReserves / 1_000_000_000 + "";
            market.price = $"{bondingCurve.CurrentPrice/1000:F9}";
        }
        if (market.programID==PumpfunAmmTransaction.ProgramId.Key) {
            var ammMarketInfo  = await PumpfunAmmTransaction.getAmmMarketInfo(market.mint);
            
            market.tokenAmount = (double)ammMarketInfo.tokenAmount / 1_000_000 + "";
            market.solAmount = (double)ammMarketInfo.solAmount / 1_000_000_000 + "";
            market.price = $"{ammMarketInfo.tokenPrice/1000:F9}";
            
        }
        if (market.programID==BonkBoundingCurveHelper.ProgramId.Key) {
            var bonk  = await BonkBoundingCurveHelper.GetBonkBoundingCurveData(market.mint);
            
            market.tokenAmount = (double)bonk.tokenAmount / 1_000_000 + "";
            market.solAmount = (double)bonk.solAmount / 1_000_000_000 + "";
            market.price = $"{bonk.price()/1000:F9}";
            
        }
        if (market.programID==BonkFunProgram.CREATE_CPMM_POOL_PROGRAM.Key) {
            var bonk  = await BonkCpmmHelper.GetPoolData(market.pool);
            market.tokenAmount = (double)bonk.tokenAmount / 1_000_000 + "";
            market.solAmount = (double)bonk.solAmount / 1_000_000_000 + "";
            market.price = $"{bonk.price()/1000:F9}";
            
        }
        return market;
    }

    public static async Task<Tuple<double,double,double>> QueryBalanceWithRPC(string TokenAddress, AccountModel accountModel) {
        Log.d($"使用RPC查询余额");
        double HoldSol = 0;
        double HoldToken = 0;
        double HoldValue = 0;
        // 创建RPC客户端
        var rpcClient = Config.rpcClient;
        
        // 创建并行任务列表
        var solBalanceTasks = new List<Task<Tuple<AccountModel, RequestResult<ResponseValue<ulong>>>>>();
        var tokenBalanceTasks = new List<Task<Tuple<AccountModel, RequestResult<ResponseValue<List<TokenAccount>>>>>>();
        // 为每个账户创建查询任务
            try {
                // 查询SOL余额
                var solBalance = await rpcClient.GetBalanceAsync(accountModel.Address);
                if (solBalance.WasSuccessful && solBalance.Result?.Value != null) {
                    // SOL余额单位是lamports，需要除以10^9转换为SOL
                    accountModel.SolBalance = solBalance.Result.Value / 1_000_000_000.0;
                    Log.d($"钱包地址: {accountModel.Address}, SOL余额: {accountModel.SolBalance}");
                    
                    HoldSol+=accountModel.SolBalance;
                } else {
                    Log.d($"获取SOL余额失败: {accountModel.Address}");
                }
                
                // 查询代币余额
                if (!string.IsNullOrEmpty(TokenAddress)) {
                    accountModel.LastTrade = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                   
                    // 获取代币账户
                    var task = rpcClient.GetTokenAccountsByOwnerAsync(
                        accountModel.Address,
                        TokenAddress,
                        null
                    );
                    await task ;
                    var tokenAccounts = task.Result;

                    if (tokenAccounts.WasSuccessful && tokenAccounts.Result?.Value != null && tokenAccounts.Result.Value.Any()) {
                        foreach (var tokenAccount in tokenAccounts.Result.Value) {
                            // 解析代币账户数据
                            Log.d(
                                $"代币信息: Account:{tokenAccount.PublicKey} Token Program:{tokenAccount.Account.Data.Program},Token Mint:{
                                    tokenAccount.Account.Data.Parsed.Info.Mint
                                } TokenAmount:{tokenAccount.Account.Data.Parsed.Info.TokenAmount}");
                            var tokenAmount = tokenAccount.Account?.Data?.Parsed?.Info?.TokenAmount;

                            // 代币余额需要根据小数位数进行转换
                            if (tokenAmount != null) {
                                accountModel.FbcBalance = tokenAmount.AmountDouble;
                                accountModel.FbcValue = 0;
                                HoldToken += accountModel.FbcBalance;
                                HoldValue += accountModel.FbcValue;
                                // accountModel.FbcValue = tokenBalance * tokenPrice;
                                Log.d(
                                    $"代币地址: {tokenAccount.Account.Data.Parsed.Info.Mint}, 余额: {accountModel.FbcBalance}, 价值: {
                                        accountModel.FbcValue}");
                            }
                        }
                    } else { Log.d($"未找到代币账户或查询失败: {accountModel.Address}, {TokenAddress}"); }
                }
            } catch (Exception ex) {
                Log.d($"查询余额出错: {ex.Message}");
               
            }
        
        Log.d($"总计 - SOL: {HoldSol}, 代币: {HoldToken}, 价值: {HoldValue}");

        return new Tuple<double, double, double>(HoldSol, HoldToken, HoldValue);
    }
    
    // 添加获取SOL余额的方法
    public static async Task<double> GetSolBalance(string address)
    {
        try
        {
            var result = await Config.rpcClient.GetBalanceAsync(new Solnet.Wallet.PublicKey(address));
            if (result.WasSuccessful)
            {
                return (double)result.Result.Value / 1_000_000_000; // 转换为SOL
            }
            return 0;
        }
        catch (Exception ex)
        {
            Log.w($"获取SOL余额失败: {ex.Message}");
            return 0;
        }
    }

    // 添加获取代币余额的方法
    public static async Task<double> GetTokenBalance(string address, string tokenMint)
    {
        try
        {
            var tokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(
                new Solnet.Wallet.PublicKey(address), 
                new Solnet.Wallet.PublicKey(tokenMint));
            
            var result = await Config.rpcClient.GetTokenAccountBalanceAsync(tokenAccount);
            if (result.WasSuccessful && result.Result?.Value != null)
            {
                return (double)result.Result.Value.AmountUlong / Math.Pow(10, result.Result.Value.Decimals);
            }
            return 0;
        }
        catch (Exception ex)
        {
            Log.w($"获取代币余额失败: {ex.Message}");
            return 0;
        }
    }
}
