namespace ChainTool.Models;

public class PoolList {

    public class RootObject {
        public bool success { get; set; }
        public Data[] data { get; set; }
        public Metadata metadata { get; set; }
    }

    public class Data {
        public string pool_id { get; set; }
        public string program_id { get; set; }
        public string program_name { get; set; }
        public string token_1 { get; set; }
        public string token_2 { get; set; }
        public string token_account_1 { get; set; }
        public string token_account_2 { get; set; }
        public int total_trades_24h { get; set; }
        public int total_volume_24h { get; set; }
    }

    public class Metadata { }

}
