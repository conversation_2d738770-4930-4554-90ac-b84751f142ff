using ChainTool.ViewModels;
using CommunityToolkit.Mvvm.ComponentModel;
using ReactiveUI;

namespace ChainTool.Models
{
    public partial class AccountModel : ViewModelBase {
        [ObservableProperty] private string address="";
        [ObservableProperty] private string privateAddress ="";
        [ObservableProperty] private double solBalance;//sol余额
        [ObservableProperty] private double fbcBalance;//代币余额
        [ObservableProperty] private double fbcRatio;//代币小数点
        [ObservableProperty] private double fbcValue;//代币价值
        [ObservableProperty] private int tradeCount;
        [ObservableProperty] private string lastTrade = "";
    }
}
