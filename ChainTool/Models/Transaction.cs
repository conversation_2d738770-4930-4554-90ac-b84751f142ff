using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Solnet.Wallet;
using Solnet.Programs;

namespace Solnet.Rpc.Models
{
    /// <summary>
    /// 表示Solana交易的类
    /// </summary>
    public class JupiterTransaction
    {
        /// <summary>
        /// 交易签名列表
        /// </summary>
        public List<string> Signatures { get; set; } = new List<string>();
        
        /// <summary>
        /// 交易消息
        /// </summary>
        public Message Message { get; set; }
        
        /// <summary>
        /// 签名者列表
        /// </summary>
        private List<Account> _signers = new List<Account>();
        
        /// <summary>
        /// 创建一个新的交易实例
        /// </summary>
        public JupiterTransaction()
        {
            Message = new Message();
        }
        
        /// <summary>
        /// 从字节数组反序列化交易
        /// </summary>
        /// <param name="data">交易数据</param>
        /// <returns>交易实例</returns>
        public static JupiterTransaction Deserialize(byte[] data)
        {
            try
            {
                var transaction = new JupiterTransaction();
                
                using (var stream = new MemoryStream(data))
                using (var reader = new BinaryReader(stream))
                {
                    // 读取签名数量
                    var signatureCount = reader.ReadByte();
                    
                    // 读取所有签名
                    for (int i = 0; i < signatureCount; i++)
                    {
                        var signatureBytes = reader.ReadBytes(64); // 签名是64字节
                        transaction.Signatures.Add(Convert.ToBase64String(signatureBytes));
                    }
                    
                    // 读取消息部分
                    var messageBytes = new byte[data.Length - stream.Position];
                    stream.Read(messageBytes, 0, messageBytes.Length);
                    
                    // 反序列化消息
                    transaction.Message = Message.Deserialize(messageBytes);
                }
                
                return transaction;
            }
            catch (Exception ex)
            {
                Shared.Log.w($"反序列化交易失败: {ex.Message}");
                throw new Exception("反序列化交易失败", ex);
            }
        }
        
        /// <summary>
        /// 序列化交易为字节数组
        /// </summary>
        /// <returns>序列化后的字节数组</returns>
        public byte[] Serialize()
        {
            try
            {
                using (var stream = new MemoryStream())
                using (var writer = new BinaryWriter(stream))
                {
                    // 写入签名数量
                    writer.Write((byte)Signatures.Count);
                    
                    // 写入所有签名
                    foreach (var signature in Signatures)
                    {
                        writer.Write(Convert.FromBase64String(signature));
                    }
                    
                    // 序列化并写入消息
                    var messageBytes = Message.Serialize();
                    writer.Write(messageBytes);
                    
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                Shared.Log.w($"序列化交易失败: {ex.Message}");
                throw new Exception("序列化交易失败", ex);
            }
        }
        
        /// <summary>
        /// 部分签名交易（添加一个签名者的签名）
        /// </summary>
        /// <param name="signer">签名者账户</param>
        public void PartialSign(Account signer)
        {
            try
            {
                // 确保签名者在交易的签名者列表中
                if (!_signers.Contains(signer))
                {
                    _signers.Add(signer);
                }
                
                // 获取消息的序列化数据
                byte[] messageBytes = Message.Serialize();
                
                // 使用签名者的私钥对消息进行签名
                byte[] signatureBytes = signer.Sign(messageBytes);
                
                // 将签名添加到交易的签名列表中
                string signature = Convert.ToBase64String(signatureBytes);
                
                // 检查是否已经有这个签名
                if (!Signatures.Contains(signature))
                {
                    Signatures.Add(signature);
                }
            }
            catch (Exception ex)
            {
                Shared.Log.w($"部分签名交易失败: {ex.Message}");
                throw new Exception("部分签名交易失败", ex);
            }
        }
    }
    
    /// <summary>
    /// 表示交易消息的类
    /// </summary>
    public class Message
    {
        /// <summary>
        /// 账户密钥列表
        /// </summary>
        public List<PublicKey> AccountKeys { get; set; } = new List<PublicKey>();
        
        /// <summary>
        /// 最近的区块哈希
        /// </summary>
        public string RecentBlockhash { get; set; }
        
        /// <summary>
        /// 指令列表
        /// </summary>
        public List<TransactionInstruction> Instructions { get; set; } = new List<TransactionInstruction>();
        
        /// <summary>
        /// 从字节数组反序列化消息
        /// </summary>
        /// <param name="data">消息数据</param>
        /// <returns>消息实例</returns>
        public static Message Deserialize(byte[] data)
        {
            try
            {
                var message = new Message();
                
                using (var stream = new MemoryStream(data))
                using (var reader = new BinaryReader(stream))
                {
                    // 读取账户数量
                    var accountCount = reader.ReadByte();
                    
                    // 读取所有账户密钥
                    for (int i = 0; i < accountCount; i++)
                    {
                        var keyBytes = reader.ReadBytes(32); // 公钥是32字节
                        message.AccountKeys.Add(new PublicKey(Base58.Encode(keyBytes)));
                    }
                    
                    // 读取最近的区块哈希
                    var blockhashBytes = reader.ReadBytes(32);
                    message.RecentBlockhash = Base58.Encode(blockhashBytes);
                    
                    // 读取指令数量
                    var instructionCount = reader.ReadByte();
                    
                    // 读取所有指令
                    for (int i = 0; i < instructionCount; i++)
                    {
                        var programIdIndex = reader.ReadByte();
                        
                        // 读取账户索引数量
                        var accountIndexCount = reader.ReadByte();
                        var accountIndices = new List<byte>();
                        
                        // 读取所有账户索引
                        for (int j = 0; j < accountIndexCount; j++)
                        {
                            accountIndices.Add(reader.ReadByte());
                        }
                        
                        // 读取数据长度
                        var dataLength = reader.ReadByte();
                        var instructionData = reader.ReadBytes(dataLength);
                        
                        // 创建指令
                        var instruction = new TransactionInstruction
                        {
                            ProgramId = message.AccountKeys[programIdIndex],
                            Keys = accountIndices.Select(idx =>  AccountMeta.Writable(message.AccountKeys[idx], true)).ToList(),
                            Data = instructionData
                        };
                        
                        message.Instructions.Add(instruction);
                    }
                }
                
                return message;
            }
            catch (Exception ex)
            {
                Shared.Log.w($"反序列化消息失败: {ex.Message}");
                throw new Exception("反序列化消息失败", ex);
            }
        }
        
        /// <summary>
        /// 序列化消息为字节数组
        /// </summary>
        /// <returns>序列化后的字节数组</returns>
        public byte[] Serialize()
        {
            try
            {
                using (var stream = new MemoryStream())
                using (var writer = new BinaryWriter(stream))
                {
                    // 写入账户数量
                    writer.Write((byte)AccountKeys.Count);
                    
                    // 写入所有账户密钥
                    foreach (var key in AccountKeys)
                    {
                        writer.Write(Base58.Decode(key.Key));
                    }
                    
                    // 写入最近的区块哈希
                    writer.Write(Base58.Decode(RecentBlockhash));
                    
                    // 写入指令数量
                    writer.Write((byte)Instructions.Count);
                    
                    // 写入所有指令
                    foreach (var instruction in Instructions)
                    {
                        // 写入程序ID索引
                        byte programIdIndex = (byte)AccountKeys.FindIndex(k => k.Key == new PublicKey(instruction.ProgramId).Key);
                        writer.Write(programIdIndex);
                        
                        // 写入账户索引数量
                        writer.Write((byte)instruction.Keys.Count);
                        
                        // 写入所有账户索引
                        foreach (var key in instruction.Keys)
                        {
                            byte accountIndex = (byte)AccountKeys.FindIndex(k => k.Key == key.PublicKey);
                            writer.Write(accountIndex);
                        }
                        
                        // 写入数据长度和数据
                        writer.Write((byte)instruction.Data.Length);
                        writer.Write(instruction.Data);
                    }
                    
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                Shared.Log.w($"序列化消息失败: {ex.Message}");
                throw new Exception("序列化消息失败", ex);
            }
        }
    }
}