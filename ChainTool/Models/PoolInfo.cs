using System.Text.Json.Serialization;
using System.Text.Json;

namespace ChainTool.Models;

public class PoolInfo {


    public class RootObject
    {
        [JsonPropertyName("success")]
        public bool success { get; set; }
        [JsonPropertyName("data")]
        public Data data { get; set; }

    }

    public class Data
    {
        [JsonPropertyName("pool_address")]
        public string pool_address { get; set; }
        [JsonPropertyName("program_id")]
        public string program_id { get; set; }
        [Json<PERSON>ropertyName("tokens_info")]
        public Tokens_info[] tokens_info { get; set; }
    }

    public class Tokens_info
    {
        [JsonPropertyName("token")]
        public string token { get; set; }
        [JsonPropertyName("token_account")]
        public string token_account { get; set; }
        [JsonPropertyName("amount")]
        public object amount { get; set; }
    }
}
