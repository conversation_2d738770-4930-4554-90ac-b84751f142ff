using System;
using System.Text;

namespace Shared {


    public class LoggerConsole:ILogger {
        
        static LoggerConsole() {
            try {
                if (!Environment.UserInteractive) {
                    Console.OutputEncoding = Encoding.UTF8;
                    Console.WriteLine("console encoding: " + Console.OutputEncoding);
                }
            } catch (Exception e) {
                Console.WriteLine(e);
            }
        }
        public void WriteLine(LoggerEntry l) {
            var logLevel = l.logLevel;
            ConsoleColor newColor = logLevel switch {
                LogLevel.Warning =>ConsoleColor.DarkYellow
              , LogLevel.Error => ConsoleColor.Red
              , LogLevel.Debug => ConsoleColor.Gray
              , LogLevel.Information => ConsoleColor.Cyan
              , _ =>ConsoleColor.Cyan
            };
            Console.ForegroundColor = newColor;
            string level = logLevel switch {
                LogLevel.Information => "I", LogLevel.Warning => "W"
              , LogLevel.Error => "E", LogLevel.Debug => "D", _ => "F",
            };
            Console.WriteLine($"[{level}][{l.time:MM/dd HH:mm:ss:fff}][{l.threadName}][{l.memberName}][{l.sourceFilePath}:{l.sourceLineNumber}] {l.message}");
            Console.ResetColor();
        }
        public void Flush() {}
    }
}
