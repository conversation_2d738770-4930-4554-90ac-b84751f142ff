using System.Runtime.CompilerServices;

namespace Shared {
#if NETCORE
    [InterpolatedStringHandler]
#endif

    public struct InterpolatedString {
        private readonly string _prefix;
        private string _result;

        public InterpolatedString(int literalLength, int formattedCount, string prefix = "") {
            _prefix = prefix;
            _result = string.Empty; // 初始化结果
        }

        public void AppendLiteral(string s) {
            _result += s; // 追加字面值
        }

        public void AppendFormatted<T>(T value) {
            _result += value?.ToString(); // 追加格式化值
        }

        public override string ToString() {
            return _prefix + _result; // 返回最终字符串
        }
    }
}
