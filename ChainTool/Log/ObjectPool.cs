using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace Shared {
    public class ObjectPool<T> where T : new() {
        private readonly ConcurrentBag<T> _objects;
        private readonly int _maxSize;

        public ObjectPool(int maxSize) {
            _maxSize = maxSize;
            _objects = new ConcurrentBag<T>();
        }

        public T Rent() {
            if (_objects.TryTake(out var item)) {
                return item; // 从池中取出一个对象
            }

            return new T(); // 如果没有可用对象，则创建新对象
        }

        public void Return(T item) {
            if (_objects.Count < _maxSize) {
                _objects.Add(item); // 将对象返回池中
            }
        }

        public void Clear() {
            _objects.Clear();
        }
    }
}
