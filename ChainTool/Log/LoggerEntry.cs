using System;
using System.IO;
using System.Threading;

namespace Shared {


public class LoggerEntry {
    public readonly static ObjectPool<LoggerEntry> pool = new ObjectPool<LoggerEntry>(10000);
#if NETCORE
    public InterpolatedString message { get; set; }
    #else
    public string message { get; set; }
#endif

    public string memberName { get; set; }
    public string threadName { get; set; }
    public string sourceFilePath { get; set; }
    public LogLevel logLevel { get; set; }
    public int sourceLineNumber { get; set; }
    public DateTime time { get; set; }


#if NETCORE
       public static LoggerEntry create(
        ref InterpolatedString message,
        LogLevel               logLevel         = LogLevel.Debug,
        string                 memberName       = "",
        string                 sourceFilePath   = "",
        int                    sourceLineNumber = 0
    ) {
        var e = pool.Rent();
        e.message = message;
        e.memberName = memberName;
        e.sourceFilePath = sourceFilePath;
        e.sourceLineNumber = sourceLineNumber;
        e.threadName = Thread.CurrentThread.Name;
        e.time = System.DateTime.Now;
        e.logLevel = logLevel;
        return e;
    }
#else

    public static LoggerEntry create(
        ref string message,
        LogLevel   logLevel         = LogLevel.Debug,
        string     memberName       = "",
        string     sourceFilePath   = "",
        int        sourceLineNumber = 0
    ) {
        var e = pool.Rent();
        e.message = message;
        e.memberName = memberName;
        e.sourceFilePath = sourceFilePath.Substring(sourceFilePath.LastIndexOf(Path.DirectorySeparatorChar) + 1);
        e.sourceLineNumber = sourceLineNumber;
        e.threadName = string.IsNullOrEmpty(Thread.CurrentThread.Name) ? Thread.CurrentThread.ManagedThreadId.ToString() : $"({Thread.CurrentThread.ManagedThreadId}){Thread.CurrentThread.Name}";
        e.time = System.DateTime.Now;
        e.logLevel = logLevel;
        return e;
    }

#endif


    public LoggerEntry() {  }

    public void Return() { pool.Return(this);}

    public override string ToString() {
        string level = logLevel switch {
            LogLevel.Information => "I", LogLevel.Warning => "W"
          , LogLevel.Error => "E", LogLevel.Debug => "D", _ => "F",
        };

        return $"[{level}][{time:MM/dd HH:mm:ss:fff}][{threadName}][{memberName}][{sourceFilePath}:{sourceLineNumber}] {message.ToString()}";

    }
}
}
