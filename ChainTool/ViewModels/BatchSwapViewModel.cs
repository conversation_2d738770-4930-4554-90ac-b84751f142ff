using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Avalonia.Controls;
using ChainTool.Models;
using ChainTool.Utils;
using ChainTool.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CsvHelper;
using System.Globalization;
using Solnet.Wallet;
using Solnet.Programs;
using Solnet.Rpc.Builders;
using System.Collections.Generic;
using System.Net.Http;
using MsBox.Avalonia;
using MsBox.Avalonia.Enums;
using Solnet.Rpc.Models;
using Transaction = Solnet.Programs.Transaction;
using Wallet = ChainTool.Models.Wallet;

namespace ChainTool.ViewModels
{
    // 转账模式枚举
    public enum TransferMode
    {
        OneToMany,
        ManyToMany,
        ManyToOne,
        Exchange,
        TokenSwap,
    }

    public partial class BatchSwapViewModel : ViewModelBase
    {
        // 转账模式
        [ObservableProperty] private TransferMode transferMode = TransferMode.OneToMany;
        
        // 列表标题
        [ObservableProperty] private string leftListTitle = "来源账号";
        [ObservableProperty] private string rightListTitle = "目标账号";
        [ObservableProperty] private string transferDirectionText = "转账";
        
        // 账号列表
        [ObservableProperty] private ObservableCollection<TransferAccountModel> leftAccounts = new();
        [ObservableProperty] private ObservableCollection<TransferAccountModel> rightAccounts = new();
        
        // 转账参数
        [ObservableProperty] private double amountPerAccount = 0.01; // 1转多：每个账户转入金额
        [ObservableProperty] private double amountPerPair = 0.01;    // 多转多：每对账户转账金额
        [ObservableProperty] private double transferPercent = 100;   // 多转1：转出比例
        [ObservableProperty] private int exchangeTypeIndex = 0;      // 批量兑换：兑换类型索引
        [ObservableProperty] private double exchangePercent = 95;    // 批量兑换：兑换比例
        [ObservableProperty] private double priorityFee = 0.0001;    // 优先级费用
        [ObservableProperty] private double slippage = 1.0;          // 滑点容忍度
        [ObservableProperty] private string tokenAddress;          // 代币地址
        [ObservableProperty] private int    tokenCount;          // 代币数量
        
        // 用于绑定到RadioButton的属性
        public bool IsOneToMany
        {
            get => TransferMode == TransferMode.OneToMany;
            set { if (value) TransferMode = TransferMode.OneToMany; }
        }
        
        public bool IsManyToMany
        {
            get => TransferMode == TransferMode.ManyToMany;
            set { if (value) TransferMode = TransferMode.ManyToMany; }
        }
        
        public bool IsManyToOne
        {
            get => TransferMode == TransferMode.ManyToOne;
            set { if (value) TransferMode = TransferMode.ManyToOne; }
        }
        
        public bool IsExchange
        {
            get => TransferMode == TransferMode.Exchange;
            set { if (value) TransferMode = TransferMode.Exchange; }
        }
        public bool IsTokenSwap
        {
            get => TransferMode == TransferMode.TokenSwap;
            set { if (value) TransferMode = TransferMode.TokenSwap; }
        }
        // 命令
        public IRelayCommand ImportLeftAccountsCommand { get; }
        public IRelayCommand ImportRightAccountsCommand { get; }
        public IRelayCommand QueryLeftBalanceCommand { get; }
        public IRelayCommand QueryRightBalanceCommand { get; }
        public IRelayCommand SelectAllLeftCommand { get; }
        public IRelayCommand UnselectAllLeftCommand { get; }
        public IRelayCommand SelectAllRightCommand { get; }
        public IRelayCommand UnselectAllRightCommand { get; }
        public IRelayCommand ExecuteCommand { get; }
        public IRelayCommand CancelCommand { get; }
        public IRelayCommand RandomAmountCommand { get; }
        
        // 日志
        public ObservableCollection<string> Logs { get; } = new();
        
        // 添加最大最小金额属性
        [ObservableProperty] private double amountMin = 0.01; // 最小转账金额
        [ObservableProperty] private double amountMax = 0.05; // 最大转账金额
        [ObservableProperty] private double totalRandomAmount = 0; // 最大转账金额
        [ObservableProperty] private ObservableCollection<double> randomAmounts = new(); // 存储每个账户的随机金额

        // 添加多转多模式的转账比例属性
        [ObservableProperty] private double manyToManyPercent = 100; // 多转多：转账比例，默认100%

        public BatchSwapViewModel()
        {
            // 初始化命令
            ImportLeftAccountsCommand = new RelayCommand(async () => await ImportAccounts(true));
            ImportRightAccountsCommand = new RelayCommand(async () => await ImportAccounts(false));
            QueryLeftBalanceCommand = new RelayCommand(async () => await QueryBalance(true));
            QueryRightBalanceCommand = new RelayCommand(async () => await QueryBalance(false));
            SelectAllLeftCommand = new RelayCommand(() => SelectAll(true, true));
            UnselectAllLeftCommand = new RelayCommand(() => SelectAll(true, false));
            SelectAllRightCommand = new RelayCommand(() => SelectAll(false, true));
            UnselectAllRightCommand = new RelayCommand(() => SelectAll(false, false));
            ExecuteCommand = new RelayCommand(async () => await ExecuteTransfer());
            CancelCommand = new RelayCommand(() => CloseWindow());
            RandomAmountCommand = new RelayCommand(GenerateRandomAmounts);
            
            // 监听转账模式变化
            this.PropertyChanged += (sender, args) => {
                if (args.PropertyName == nameof(TransferMode))
                {
                    UpdateUIBasedOnTransferMode();
                }
                else if (args.PropertyName == nameof(ManyToManyPercent))
                {
                    // 当多转多比例变化时，更新转账金额
                    if (TransferMode == TransferMode.ManyToMany)
                    {
                        UpdateManyToManyAmounts();
                    }
                }
                else if (args.PropertyName == nameof(TransferPercent))
                {
                    // 当多转1比例变化时，更新转账金额
                    if (TransferMode == TransferMode.ManyToOne)
                    {
                        UpdateManyToOneAmounts();
                    }
                }
            };
        }
        
        private void UpdateUIBasedOnTransferMode()
        {
            // 清空左右两个列表的数据
            LeftAccounts.Clear();
            RightAccounts.Clear();
            
            // 清空随机金额列表
            RandomAmounts.Clear();
            TotalRandomAmount = 0;
            
            switch (TransferMode)
            {
                case TransferMode.OneToMany:
                    LeftListTitle = "来源账号";
                    RightListTitle = "目标账号";
                    TransferDirectionText = "转账";
                    break;
                case TransferMode.ManyToMany:
                    LeftListTitle = "来源账号";
                    RightListTitle = "目标账号";
                    TransferDirectionText = "一对一转账";
                    break;
                case TransferMode.ManyToOne:
                    LeftListTitle = "来源账号";
                    RightListTitle = "目标账号";
                    TransferDirectionText = "转账";
                    break;
                case TransferMode.Exchange:
                    LeftListTitle = "账号列表";
                    RightListTitle = "兑换信息";
                    TransferDirectionText = "兑换";
                    break;
                
                case TransferMode.TokenSwap:
                    LeftListTitle = "发送列表";
                    RightListTitle = "接收列表";
                    TransferDirectionText = "转账";
                    break;
            }
            
            // 记录日志
            Log($"已切换到{TransferMode}模式，列表数据已清空");
            
            // 通知UI更新
            OnPropertyChanged(nameof(IsOneToMany));
            OnPropertyChanged(nameof(IsManyToMany));
            OnPropertyChanged(nameof(IsManyToOne));
            OnPropertyChanged(nameof(IsExchange));
            OnPropertyChanged(nameof(IsTokenSwap));
        }
        
        private async Task ImportAccounts(bool isLeft)
        {
            var dialog = new OpenFileDialog { 
                AllowMultiple = false, 
                Filters = { new FileDialogFilter() { Name = "CSV 文件", Extensions = { "csv" } } } 
            };
            
            var result = await dialog.ShowAsync(App.MainWindow);
            
            if (result != null && result.Length > 0)
            {
                try
                {
                    using (var reader = new StreamReader(result[0]))
                    using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
                    {
                        var records = csv.GetRecords<Wallet>().ToList();
                        
                        var accounts = new ObservableCollection<TransferAccountModel>(
                            records.Select(w => new TransferAccountModel 
                            { 
                                Address = w.publicKey, 
                                PrivateKey = w.privateKey,
                                IsSelected = true
                            })
                        );
                        
                        if (isLeft)
                        {
                            LeftAccounts = accounts;
                            Log($"已导入 {accounts.Count} 个来源账号");
                        }
                        else
                        {
                            RightAccounts = accounts;
                            Log($"已导入 {accounts.Count} 个目标账号");
                        }
                        
                        // 导入后自动查询余额
                        await QueryBalance(isLeft);
                        
                        // 根据当前模式更新转账金额
                        if (TransferMode == TransferMode.ManyToMany)
                        {
                            UpdateManyToManyAmounts();
                        }
                        else if (TransferMode == TransferMode.ManyToOne)
                        {
                            UpdateManyToOneAmounts();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Toast.Show(this, $"导入账号失败: {ex.Message}");
                    Log($"导入账号失败: {ex.Message}");
                }
            }
        }
        
        private async Task QueryBalance(bool isLeft)
        {
            var accounts = isLeft ? LeftAccounts : RightAccounts;
            
            if (accounts.Count == 0)
            {
                Toast.Show(this, "没有账号可查询");
                return;
            }
            
            Log($"开始查询{(isLeft ? "来源" : "目标")}账号余额...");
            
            var tasks = new List<Task>();
            
            foreach (var account in accounts)
            {
                tasks.Add(Task.Run(async () => {
                    try
                    {
                        // 查询SOL余额
                        var solBalance = await API.GetSolBalance(account.Address);

                        account.SolBalance = solBalance;
                        
                        // 查询USDC余额
                        var usdcBalance = await API.GetTokenBalance(account.Address, TransferMode==TransferMode.TokenSwap?TokenAddress:Config.USDC_MINT);
                        account.UsdcBalance = usdcBalance;
                    }
                    catch (Exception ex)
                    {
                        Log($"查询账号 {account.Address} 余额失败: {ex.Message}");
                    }
                }));
            }
            
            await Task.WhenAll(tasks);
            Log($"完成查询{(isLeft ? "来源" : "目标")}账号余额");
            
            // 根据当前模式更新转账金额
            if (TransferMode == TransferMode.ManyToMany)
            {
                UpdateManyToManyAmounts();
            }
            else if (TransferMode == TransferMode.ManyToOne)
            {
                UpdateManyToOneAmounts();
            }
        }
        
        private void SelectAll(bool isLeft, bool select)
        {
            var accounts = isLeft ? LeftAccounts : RightAccounts;
            
            foreach (var account in accounts)
            {
                account.IsSelected = select;
            }
            
            Log($"{(isLeft ? "来源" : "目标")}账号已{(select ? "全选" : "取消全选")}");
            
            // 根据当前模式更新转账金额
            if (TransferMode == TransferMode.ManyToMany)
            {
                UpdateManyToManyAmounts();
            }
            else if (TransferMode == TransferMode.ManyToOne)
            {
                UpdateManyToOneAmounts();
            }
        }
        
        private async Task ExecuteTransfer()
        {
            try
            {
                if (IsOneToMany)
                {
                    await ExecuteOneToMany();
                }
                else if (IsManyToMany)
                {
                    await ExecuteManyToMany();
                }
                else if (IsManyToOne)
                {
                    await ExecuteManyToOne();
                }
                else if (IsExchange)
                {
                    await ExecuteExchange();
                }
                else if (IsTokenSwap)
                {
                    await ExecuteTokenSwap();
                }
            }
            catch (Exception ex)
            {
                Toast.Show(this, $"执行失败: {ex.Message}");
                Log($"执行失败: {ex.Message}");
            }
        }
        
        private async Task ExecuteOneToMany()
        {
            // 验证参数
            var sourceAccounts = LeftAccounts.ToList();
            var targetAccounts = RightAccounts.ToList();
            
            if (sourceAccounts.Count == 0)
            {
                Toast.Show(this, "请导入至少一个来源账号");
                return;
            }
            
            if (targetAccounts.Count == 0)
            {
                Toast.Show(this, "请导入至少一个目标账号");
                return;
            }
            
            // 检查是否已生成随机金额
            if (RandomAmounts.Count == 0 || RandomAmounts.Count != targetAccounts.Count)
            {
                // 如果没有生成随机金额，则使用最小值
                RandomAmounts.Clear();
                foreach (var account in targetAccounts)
                {
                    RandomAmounts.Add(AmountMin);
                }
                Log($"使用最小金额 {AmountMin:F3} SOL 作为每个账户的转账金额");
            }
            
            // 计算总金额
            double totalAmount = RandomAmounts.Sum();
            
            // 确认操作
            var confirmResult = await ShowConfirmDialog(
                $"确认从 {sourceAccounts[0].Address} 向 {targetAccounts.Count} 个账号转入总计 {totalAmount:F3} SOL？");
            
            if (!confirmResult)
                return;
            
            Log($"开始执行1转多操作...");
            
            // 检查余额
            if (sourceAccounts[0].SolBalance < totalAmount + 0.01) // 预留0.01 SOL作为手续费
            {
                Toast.Show(this, $"来源账号余额不足，需要至少 {totalAmount + 0.01} SOL");
                Log($"来源账号余额不足，需要至少 {totalAmount + 0.01} SOL");
                return;
            }
            
            // 执行转账
            var sourceWallet = WalletHelper.CreateAccountFromKeyPair(
                sourceAccounts[0].PrivateKey, sourceAccounts[0].Address);
            
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < targetAccounts.Count; i++)
            {
                var targetAccount = targetAccounts[i];
                double transferAmount = RandomAmounts[i];
                
                try
                {
                    // 创建转账指令
                    var instruction = SystemProgram.Transfer(
                        sourceWallet.PublicKey,
                        new PublicKey(targetAccount.Address),
                        (ulong)(transferAmount * 1_000_000_000) // 转换为lamports
                    );
                    
                    
                    // 获取最新区块哈希
                    var blockHash = await Config.rpcClient.GetLatestBlockHashAsync();
                    if (!blockHash.WasSuccessful)
                    {
                        Log($"获取区块哈希失败: {blockHash.Reason}");
                        failCount++;
                        continue;
                    }
                    
                    // 构建交易
                    var tx = new TransactionBuilder()
                        .SetRecentBlockHash(blockHash.Result.Value.Blockhash)
                        .SetFeePayer(sourceWallet)
                        .AddInstruction(instruction)
                        .Build(sourceWallet);
                    
                    // 发送交易
                    var result = await Config.rpcClient.SendTransactionAsync(tx);
                    
                    if (result.WasSuccessful)
                    {
                        successCount++;
                        Log($"转账成功: {sourceAccounts[0].Address} -> {targetAccount.Address}, {transferAmount:F3} SOL, 交易哈希: {result.Result}");
                    }
                    else
                    {
                        failCount++;
                        Log($"转账失败: {sourceAccounts[0].Address} -> {targetAccount.Address}, 原因: {result.Reason}");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    Log($"转账异常: {sourceAccounts[0].Address} -> {targetAccount.Address}, 原因: {ex.Message}");
                }
            }
            
            // 显示结果统计
            Log($"1转多操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalAmount:F3} SOL");
            Toast.Show(this, $"操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalAmount:F3} SOL");
            
            // 刷新余额
            await QueryBalance(true);
            await QueryBalance(false);
        }
        
        private async Task ExecuteTokenSwap()
        {
            // 验证参数
            var sourceAccounts = LeftAccounts.ToList();
            var targetAccounts = RightAccounts.ToList();
            
            if (sourceAccounts.Count == 0)
            {
                Toast.Show(this, "请导入至少一个来源账号");
                return;
            }
            
            if (targetAccounts.Count == 0)
            {
                Toast.Show(this, "请导入至少一个目标账号");
                return;
            }
            
            // 检查是否已生成随机金额
            if (TokenCount <= 0 )
            {
                Toast.Show(this,"代币数量需大于1");
            }
            
            
            // 检查余额
            if (sourceAccounts[0].SolBalance <  + 0.01) // 预留0.01 SOL作为手续费
            {
                Toast.Show(this, $"账号余额不足，需要至少 { 0.01} SOL");
                Log($"账号余额不足，需要至少 { 0.01} SOL");
                return;
            }
            
            // 执行转账
            var sourceWallet = WalletHelper.CreateAccountFromKeyPair(
                sourceAccounts[0].PrivateKey, sourceAccounts[0].Address);
            
            int successCount = 0;
            int failCount = 0;
            double totalAmount = 0;
            for (int i = 0; i < targetAccounts.Count; i++)
            {
                var targetAccount = targetAccounts[i];
                double transferAmount = TokenCount;
                try
                {
                    // 创建转账指令
                    var instruction = SystemProgram.Transfer(
                        sourceWallet.PublicKey,
                        new PublicKey(targetAccount.Address),
                        (ulong)(transferAmount) // 转换为lamports
                    );
                    
                    totalAmount+=transferAmount;
                    // 获取最新区块哈希
                    var blockHash = await Config.rpcClient.GetLatestBlockHashAsync();
                    if (!blockHash.WasSuccessful)
                    {
                        Log($"获取区块哈希失败: {blockHash.Reason}");
                        failCount++;
                        continue;
                    }
                    
                    // 构建交易
                    var tx = new TransactionBuilder()
                        .SetRecentBlockHash(blockHash.Result.Value.Blockhash)
                        .SetFeePayer(sourceWallet)
                        .AddInstruction(instruction)
                        .Build(sourceWallet);
                    
                    // 发送交易
                    var result = await Config.rpcClient.SendTransactionAsync(tx);
                    
                    if (result.WasSuccessful)
                    {
                        successCount++;
                        
                        Log($"转账成功: {sourceAccounts[0].Address} -> {targetAccount.Address}, {transferAmount:F3} SOL, 交易哈希: {result.Result}");
                    }
                    else
                    {
                        failCount++;
                        Log($"转账失败: {sourceAccounts[0].Address} -> {targetAccount.Address}, 原因: {result.Reason}");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    Log($"转账异常: {sourceAccounts[0].Address} -> {targetAccount.Address}, 原因: {ex.Message}");
                }
            }
            
            // 显示结果统计
           
            Log($"1转多操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalAmount:F3} SOL");
            Toast.Show(this, $"操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalAmount:F3} SOL");
            
            // 刷新余额
            await QueryBalance(true);
            await QueryBalance(false);
        }
        
        private async Task ExecuteManyToMany()
        {
            // 验证参数
            var sourceAccounts = LeftAccounts.Where(a => a.IsSelected).ToList();
            var targetAccounts = RightAccounts.ToList();
            
            if (sourceAccounts.Count == 0)
            {
                Toast.Show(this, "请选择至少一个来源账号");
                return;
            }
            
            if (targetAccounts.Count == 0)
            {
                Toast.Show(this, "请导入至少一个目标账号");
                return;
            }
            
            if (ManyToManyPercent <= 0 || ManyToManyPercent > 100)
            {
                Toast.Show(this, "转账比例必须在0.001%-100%之间");
                return;
            }
            
            // 确认操作
            var confirmResult = await ShowConfirmDialog(
                $"确认从 {sourceAccounts.Count} 个账号向 {targetAccounts.Count} 个账号一对一转账，每个账号转出 {ManyToManyPercent}% 的SOL余额？");
            
            if (!confirmResult)
                return;
            
            Log($"开始执行多转多操作...");
            
            // 确定实际转账对数
            int pairCount = Math.Min(sourceAccounts.Count, targetAccounts.Count);
            
            int successCount = 0;
            int failCount = 0;
            double totalTransferred = 0;
            
            // 执行转账
            for (int i = 0; i < pairCount; i++)
            {
                var sourceAccount = sourceAccounts[i];
                var targetAccount = targetAccounts[i];
                
                // 计算转账金额（账户余额的百分比）
                double transferAmount = sourceAccount.SolBalance * (ManyToManyPercent / 100.0);
                
                // 确保至少保留0.0011 SOL作为手续费和账号留存
                double minReserve = 0.0011;
                if (transferAmount > sourceAccount.SolBalance - minReserve)
                {
                    transferAmount = sourceAccount.SolBalance - minReserve;
                    if (transferAmount <= 0)
                    {
                        Log($"账号 {sourceAccount.Address} 余额不足，跳过");
                        failCount++;
                        continue;
                    }
                }
                
                // 更新转出和接收金额
                sourceAccount.FromAmount = transferAmount;
                targetAccount.ToAmount = transferAmount;
                
                try
                {
                    var sourceWallet = WalletHelper.CreateAccountFromKeyPair(
                        sourceAccount.PrivateKey, sourceAccount.Address);
                    
                    // 创建转账指令
                    var instruction = SystemProgram.Transfer(
                        sourceWallet.PublicKey,
                        new PublicKey(targetAccount.Address),
                        (ulong)(transferAmount * 1_000_000_000) // 转换为lamports
                    );
                    
                    // 添加优先级费用指令
                    var priorityFeeInstruction = ComputeBudgetProgram.SetComputeUnitPrice(
                        (ulong)(PriorityFee * 1_000_000_000)
                    );
                    
                    // 获取最新区块哈希
                    var blockHash = await Config.rpcClient.GetLatestBlockHashAsync();
                    if (!blockHash.WasSuccessful)
                    {
                        Log($"获取区块哈希失败: {blockHash.Reason}");
                        failCount++;
                        continue;
                    }
                    
                    // 构建交易
                    var tx = new TransactionBuilder()
                        .SetRecentBlockHash(blockHash.Result.Value.Blockhash)
                        .SetFeePayer(sourceWallet)
                        .AddInstruction(priorityFeeInstruction)
                        .AddInstruction(instruction)
                        .Build(sourceWallet);
                    
                    // 发送交易
                    var result = await Config.rpcClient.SendTransactionAsync(tx);
                    
                    if (result.WasSuccessful)
                    {
                        successCount++;
                        totalTransferred += transferAmount;
                        Log($"转账成功: {sourceAccount.Address} -> {targetAccount.Address}, {transferAmount:F6} SOL, 交易哈希: {result.Result}");
                    }
                    else
                    {
                        failCount++;
                        Log($"转账失败: {sourceAccount.Address} -> {targetAccount.Address}, 原因: {result.Reason}");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    Log($"转账异常: {sourceAccount.Address} -> {targetAccount.Address}, 原因: {ex.Message}");
                }
            }
            
            // 显示结果统计
            Log($"多转多操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalTransferred:F6} SOL");
            Toast.Show(this, $"操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalTransferred:F6} SOL");
            
            // 刷新余额
            await QueryBalance(true);
            await QueryBalance(false);
        }
        
        private async Task ExecuteManyToOne()
        {
            // 验证参数
            var sourceAccounts = LeftAccounts.Where(a => a.IsSelected).ToList();
            var targetAccounts = RightAccounts.Where(a => a.IsSelected).ToList();
            
            if (sourceAccounts.Count == 0)
            {
                Toast.Show(this, "请选择至少一个来源账号");
                return;
            }
            
            if (targetAccounts.Count == 0)
            {
                Toast.Show(this, "请选择至少一个目标账号");
                return;
            }
            
            if (targetAccounts.Count > 1)
            {
                Toast.Show(this, "多转1模式下，请只选择一个目标账号");
                return;
            }
            
            if (TransferPercent <= 0 || TransferPercent > 100)
            {
                Toast.Show(this, "转出比例必须在1-100%之间");
                return;
            }
            
            // 确认操作
            var confirmResult = await ShowConfirmDialog(
                $"确认从 {sourceAccounts.Count} 个账号向 {targetAccounts[0].Address} 转出 {TransferPercent}% 的SOL？");
            
            if (!confirmResult)
                return;
            
            Log($"开始执行多转1操作...");
            
            int successCount = 0;
            int failCount = 0;
            double totalTransferred = 0;
            
            // 执行转账
            foreach (var sourceAccount in sourceAccounts)
            {
                // 计算转账金额
                double transferAmount = sourceAccount.SolBalance * (TransferPercent / 100.0);
                
                // 保留一些SOL作为手续费
                double reserveAmount = 0.0011;
                if (transferAmount > sourceAccount.SolBalance - reserveAmount)
                {
                    transferAmount = sourceAccount.SolBalance - reserveAmount;
                }
                
                if (transferAmount <= 0)
                {
                    Log($"账号 {sourceAccount.Address} 余额不足，跳过");
                    failCount++;
                    continue;
                }
                
                try
                {
                    var sourceWallet = WalletHelper.CreateAccountFromKeyPair(
                        sourceAccount.PrivateKey, sourceAccount.Address);
                    
                    // 创建转账指令
                    var instruction = SystemProgram.Transfer(
                        sourceWallet.PublicKey,
                        new PublicKey(targetAccounts[0].Address),
                        (ulong)(transferAmount * 1_000_000_000) // 转换为lamports
                    );
                    
                    // 添加优先级费用指令
                    var priorityFeeInstruction = ComputeBudgetProgram.SetComputeUnitPrice(
                        (ulong)(PriorityFee * 1_000_000_000)
                    );
                    
                    // 获取最新区块哈希
                    var blockHash = await Config.rpcClient.GetLatestBlockHashAsync();
                    if (!blockHash.WasSuccessful)
                    {
                        Log($"获取区块哈希失败: {blockHash.Reason}");
                        failCount++;
                        continue;
                    }
                    
                    // 构建交易
                    var tx = new TransactionBuilder()
                        .SetRecentBlockHash(blockHash.Result.Value.Blockhash)
                        .SetFeePayer(sourceWallet)
                        .AddInstruction(priorityFeeInstruction)
                        .AddInstruction(instruction)
                        .Build(sourceWallet);
                    
                    // 发送交易
                    var result = await Config.rpcClient.SendTransactionAsync(tx);
                    
                    if (result.WasSuccessful)
                    {
                        successCount++;
                        totalTransferred += transferAmount;
                        Log($"转账成功: {sourceAccount.Address} -> {targetAccounts[0].Address}, {transferAmount:F6} SOL, 交易哈希: {result.Result}");
                    }
                    else
                    {
                        failCount++;
                        Log($"转账失败: {sourceAccount.Address} -> {targetAccounts[0].Address}, 原因: {result.Reason}");
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    Log($"转账异常: {sourceAccount.Address} -> {targetAccounts[0].Address}, 原因: {ex.Message}");
                }
            }
            
            // 显示结果统计
            Log($"多转1操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalTransferred:F6} SOL");
            Toast.Show(this, $"操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔, 总计转出 {totalTransferred:F6} SOL");
            
            // 刷新余额
            await QueryBalance(true);
            await QueryBalance(false);
        }
        
        private async Task ExecuteExchange()
        {
            // 验证参数
            var accounts = LeftAccounts.Where(a => a.IsSelected).ToList();
            
            if (accounts.Count == 0)
            {
                Toast.Show(this, "请选择至少一个账号");
                return;
            }
            
            if (ExchangePercent <= 0 || ExchangePercent > 100)
            {
                Toast.Show(this, "兑换比例必须在1-100%之间");
                return;
            }
            
            // 检查左右列表的账号是否相同
            var leftAddresses = LeftAccounts.Select(a => a.Address).ToHashSet();
            var rightAddresses = RightAccounts.Select(a => a.Address).ToHashSet();
            
            // 如果右侧列表不为空，且与左侧列表不完全相同
            if (RightAccounts.Count > 0 && (!leftAddresses.SetEquals(rightAddresses) || leftAddresses.Count != rightAddresses.Count))
            {
                Toast.Show(this, "兑换只能发生在同一个账号之间");
                return;
            }
            
            bool isSolToUsdc = ExchangeTypeIndex == 0;
            string fromToken = isSolToUsdc ? "SOL" : "USDC";
            string toToken = isSolToUsdc ? "USDC" : "SOL";
            
            // 确认操作
            var confirmResult = await ShowConfirmDialog(
                $"确认将 {accounts.Count} 个账号中的 {ExchangePercent}% 的 {fromToken} 兑换为 {toToken}？");
            
            if (!confirmResult)
                return;
            
            Log($"开始执行批量兑换操作: {fromToken} -> {toToken}...");
            
            int successCount = 0;
            int failCount = 0;
            
            // 执行兑换
            foreach (var account in accounts)
            {
                try
                {
                    if (isSolToUsdc)
                    {
                        // SOL -> USDC
                        double solAmount = account.SolBalance * (ExchangePercent / 100.0);
                        
                        // 保留一些SOL作为手续费
                        double reserveAmount = 0.01;
                        if (solAmount > account.SolBalance - reserveAmount)
                        {
                            solAmount = account.SolBalance - reserveAmount;
                        }
                        
                        if (solAmount <= 0)
                        {
                            Log($"账号 {account.Address} SOL余额不足，跳过");
                            failCount++;
                            continue;
                        }
                        
                        // 调用Jupiter API进行兑换
                        var result = await ExchangeSolToUsdc(account, solAmount);
                        
                        if (result)
                        {
                            successCount++;
                            Log($"兑换成功: {account.Address}, {solAmount:F6} SOL -> USDC");
                        }
                        else
                        {
                            failCount++;
                            Log($"兑换失败: {account.Address}, {solAmount:F6} SOL -> USDC");
                        }
                    }
                    else
                    {
                        // USDC -> SOL
                        double usdcAmount = account.UsdcBalance * (ExchangePercent / 100.0);
                        
                        if (usdcAmount <= 0)
                        {
                            Log($"账号 {account.Address} USDC余额不足，跳过");
                            failCount++;
                            continue;
                        }
                        
                        // 调用Jupiter API进行兑换
                        var result = await ExchangeUsdcToSol(account, usdcAmount);
                        
                        if (result)
                        {
                            successCount++;
                            Log($"兑换成功: {account.Address}, {usdcAmount:F6} USDC -> SOL");
                        }
                        else
                        {
                            failCount++;
                            Log($"兑换失败: {account.Address}, {usdcAmount:F6} USDC -> SOL");
                        }
                    }
                }
                catch (Exception ex)
                {
                    failCount++;
                    Log($"兑换异常: {account.Address}, 原因: {ex.Message}");
                }
            }
            
            // 显示结果统计
            Log($"批量兑换操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔");
            Toast.Show(this, $"操作完成: 成功 {successCount} 笔, 失败 {failCount} 笔");
            
            // 刷新余额
            await QueryBalance(true);
        }
        
        private async Task<bool> ExchangeSolToUsdc(TransferAccountModel account, double solAmount)
        {
            try
            {
                Log($"开始兑换: {account.Address} 的 {solAmount:F6} SOL -> USDC");
                
                // 创建钱包
                var wallet = WalletHelper.CreateAccountFromKeyPair(account.PrivateKey, account.Address);
                
                // 检查用户是否有USDC的关联账户，如果没有则创建
                PublicKey usdcMint = new PublicKey(Config.TOKEN_USDC);
                PublicKey associatedTokenAccount = AssociatedTokenAccountProgram.DeriveAssociatedTokenAccount(wallet.PublicKey, usdcMint);
                
                // 查询关联代币账户是否存在
                var accountInfo = await Config.rpcClient.GetAccountInfoAsync(associatedTokenAccount);
                bool ataExists = accountInfo.WasSuccessful && accountInfo.Result?.Value != null;
                
                // 构建交易
                var txBuilder = new TransactionBuilder()
                    .SetRecentBlockHash((await Config.rpcClient.GetLatestBlockHashAsync()).Result.Value.Blockhash)
                    .SetFeePayer(wallet);
                
                // 如果关联代币账户不存在，添加创建指令
                if (!ataExists)
                {
                    Log($"USDC关联代币账户不存在，正在创建: {associatedTokenAccount}");
                    
                    // 创建关联代币账户的指令
                    TransactionInstruction createAtaInstruction = AssociatedTokenAccountProgram.CreateAssociatedTokenAccount(
                        wallet.PublicKey,    // 付款人
                        wallet.PublicKey,    // 所有者
                        usdcMint             // 代币Mint
                    );
                    
                    txBuilder.AddInstruction(createAtaInstruction);
                }
                
                // 创建WSOL账户（包装SOL）
                Account wsolAccount = new Account();
                
                // 计算所需空间和租金
                ulong rentExemptionAmount = (await Config.rpcClient.GetMinimumBalanceForRentExemptionAsync(TokenProgram.TokenAccountDataSize)).Result;
                
                // 创建WSOL账户的指令
                TransactionInstruction createWsolAccountInstruction = SystemProgram.CreateAccount(
                    wallet.PublicKey,
                    wsolAccount.PublicKey,
                    rentExemptionAmount + (ulong)(solAmount * 1_000_000_000), // 租金 + 兑换金额
                    TokenProgram.TokenAccountDataSize,
                    TokenProgram.ProgramIdKey
                );
                
                // 初始化WSOL账户的指令
                TransactionInstruction initWsolAccountInstruction = TokenProgram.InitializeAccount(
                    wsolAccount.PublicKey,
                    new PublicKey("So11111111111111111111111111111111111111112"), // WSOL Mint地址
                    wallet.PublicKey
                );
                
                // 使用Jupiter API获取兑换路由
                string jupiterApiUrl = $"https://quote-api.jup.ag/v6/quote?inputMint=So11111111111111111111111111111111111111112&outputMint={Config.TOKEN_USDC}&amount={(ulong)(solAmount * 1_000_000_000)}&slippageBps={Slippage * 100}";
                
                using (HttpClient client = new HttpClient())
                {
                    var response = await client.GetAsync(jupiterApiUrl);
                    if (!response.IsSuccessStatusCode)
                    {
                        Log($"获取Jupiter兑换路由失败: {response.StatusCode}");
                        return false;
                    }
                    
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var quoteData = System.Text.Json.JsonSerializer.Deserialize<JupiterQuoteResponse>(jsonResponse);
                    
                    if (quoteData == null || quoteData.routePlan==null||quoteData.routePlan.Length==0)
                    {
                        Log("无法获取有效的兑换路由");
                        return false;
                    }
                    
                    // 获取兑换交易指令
                    string swapApiUrl = "https://quote-api.jup.ag/v6/swap";
                    var swapRequest = new
                    {
                        quoteResponse = quoteData,
                        userPublicKey = wallet.PublicKey.Key,
                        wrapUnwrapSOL = true
                    };
                    
                    var swapContent = new StringContent(
                        System.Text.Json.JsonSerializer.Serialize(swapRequest),
                        System.Text.Encoding.UTF8,
                        "application/json"
                    );
                    
                    var swapResponse = await client.PostAsync(swapApiUrl, swapContent);
                    if (!swapResponse.IsSuccessStatusCode)
                    {
                        Log($"获取Jupiter兑换交易失败: {swapResponse.StatusCode}");
                        return false;
                    }
                    
                    var swapJsonResponse = await swapResponse.Content.ReadAsStringAsync();
                    var swapData = System.Text.Json.JsonSerializer.Deserialize<JupiterSwapResponse>(swapJsonResponse);
                    
                    if (swapData == null || string.IsNullOrEmpty(swapData.swapTransaction))
                    {
                        Log("无法获取有效的兑换交易");
                        return false;
                    }
                    
                    // 解码并签名交易
                    byte[] transactionData = Convert.FromBase64String(swapData.swapTransaction);
                    JupiterTransaction swapTransaction = JupiterTransaction.Deserialize(transactionData);
                    
                    // 签名并发送交易
                    swapTransaction.PartialSign(wallet);
                    
                    var serializedTransaction = swapTransaction.Serialize();
                    var base64Transaction = Convert.ToBase64String(serializedTransaction);
                    
                    // 发送签名后的交易
                    var sendRequest = new
                    {
                        jsonrpc = "2.0",
                        id = 1,
                        method = "sendTransaction",
                        @params = new object[]
                        {
                            base64Transaction,
                            new { skipPreflight = true, maxRetries = 3 }
                        }
                    };
                    
                    var sendContent = new StringContent(
                        System.Text.Json.JsonSerializer.Serialize(sendRequest),
                        System.Text.Encoding.UTF8,
                        "application/json"
                    );
                    
                    var sendResponse = await client.PostAsync(Config.rpcClient.NodeAddress, sendContent);
                    if (!sendResponse.IsSuccessStatusCode)
                    {
                        Log($"发送交易失败: {sendResponse.StatusCode}");
                        return false;
                    }
                    
                    var sendJsonResponse = await sendResponse.Content.ReadAsStringAsync();
                    var sendData = System.Text.Json.JsonSerializer.Deserialize<JupiterSendResponse>(sendJsonResponse);
                    
                    if (sendData == null || sendData.error != null)
                    {
                        Log($"交易失败: {(sendData?.error?.message ?? "未知错误")}");
                        return false;
                    }
                    
                    Log($"兑换成功: {account.Address} 的 {solAmount:F6} SOL -> USDC, 交易哈希: {sendData.result}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log($"兑换SOL到USDC失败: {ex.Message}");
                return false;
            }
        }
        
        private async Task<bool> ExchangeUsdcToSol(TransferAccountModel account, double usdcAmount)
        {
            try
            {
                Log($"开始兑换: {account.Address} 的 {usdcAmount:F6} USDC -> SOL");
                
                // 创建钱包
                var wallet = WalletHelper.CreateAccountFromKeyPair(account.PrivateKey, account.Address);
                
                // 设置最大交易计算单元
                uint computeUnits = 200_000;
                TransactionInstruction computeUnitLimitInstruction = ComputeBudgetProgram.SetComputeUnitLimit(computeUnits);
                
                // 创建优先交易费的指令（使用默认值0.0001 SOL）
                double priorityFee = 0.0001;
                ulong priorityRate = (ulong)(priorityFee * 1_000_000_000 / computeUnits);
                TransactionInstruction priorityFeeInstruction = ComputeBudgetProgram.SetComputeUnitPrice(priorityRate);
                
                // 使用Jupiter API获取兑换路由
                string jupiterApiUrl = $"https://quote-api.jup.ag/v6/quote?inputMint={Config.TOKEN_USDC}&outputMint=So11111111111111111111111111111111111111112&amount={(ulong)(usdcAmount * 1_000_000)}&slippageBps={Slippage * 100}";
                
                using (HttpClient client = new HttpClient())
                {
                    var response = await client.GetAsync(jupiterApiUrl);
                    if (!response.IsSuccessStatusCode)
                    {
                        Log($"获取Jupiter兑换路由失败: {response.StatusCode}");
                        return false;
                    }
                    
                    var jsonResponse = await response.Content.ReadAsStringAsync();
                    var quoteData = System.Text.Json.JsonSerializer.Deserialize<JupiterQuoteResponse>(jsonResponse);
                    
                    if (quoteData == null || quoteData.routePlan==null||quoteData.routePlan.Length==0)
                    {
                        Log("无法获取有效的兑换路由");
                        return false;
                    }
                    
                    // 获取兑换交易指令
                    string swapApiUrl = "https://quote-api.jup.ag/v6/swap";
                    var swapRequest = new
                    {
                        quoteResponse = quoteData,
                        userPublicKey = wallet.PublicKey.Key,
                        wrapUnwrapSOL = true
                    };
                    
                    var swapContent = new StringContent(
                        System.Text.Json.JsonSerializer.Serialize(swapRequest),
                        System.Text.Encoding.UTF8,
                        "application/json"
                    );
                    
                    var swapResponse = await client.PostAsync(swapApiUrl, swapContent);
                    if (!swapResponse.IsSuccessStatusCode)
                    {
                        Log($"获取Jupiter兑换交易失败: {swapResponse.StatusCode}");
                        return false;
                    }
                    
                    var swapJsonResponse = await swapResponse.Content.ReadAsStringAsync();
                    var swapData = System.Text.Json.JsonSerializer.Deserialize<JupiterSwapResponse>(swapJsonResponse);
                    
                    if (swapData == null || string.IsNullOrEmpty(swapData.swapTransaction))
                    {
                        Log("无法获取有效的兑换交易");
                        return false;
                    }
                    
                    // 解码并签名交易
                    byte[] transactionData = Convert.FromBase64String(swapData.swapTransaction);
                    JupiterTransaction swapTransaction = JupiterTransaction.Deserialize(transactionData);
                    
                    // 签名并发送交易
                    swapTransaction.PartialSign(wallet);
                    
                    var serializedTransaction = swapTransaction.Serialize();
                    var base64Transaction = Convert.ToBase64String(serializedTransaction);
                    
                    // 发送签名后的交易
                    var sendRequest = new
                    {
                        jsonrpc = "2.0",
                        id = 1,
                        method = "sendTransaction",
                        @params = new object[]
                        {
                            base64Transaction,
                            new { skipPreflight = true, maxRetries = 3 }
                        }
                    };
                    
                    var sendContent = new StringContent(
                        System.Text.Json.JsonSerializer.Serialize(sendRequest),
                        System.Text.Encoding.UTF8,
                        "application/json"
                    );
                    
                    var sendResponse = await client.PostAsync(Config.rpcClient.NodeAddress, sendContent);
                    if (!sendResponse.IsSuccessStatusCode)
                    {
                        Log($"发送交易失败: {sendResponse.StatusCode}");
                        return false;
                    }
                    
                    var sendJsonResponse = await sendResponse.Content.ReadAsStringAsync();
                    var sendData = System.Text.Json.JsonSerializer.Deserialize<JupiterSendResponse>(sendJsonResponse);
                    
                    if (sendData == null || sendData.error != null)
                    {
                        Log($"交易失败: {(sendData?.error?.message ?? "未知错误")}");
                        return false;
                    }
                    
                    Log($"兑换成功: {account.Address} 的 {usdcAmount:F6} USDC -> SOL, 交易哈希: {sendData.result}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log($"兑换USDC到SOL失败: {ex.Message}");
                return false;
            }
        }
        
        private void Log(string message)
        {
            Logs.Add($"[{DateTime.Now:HH:mm:ss}] {message}");
            Shared.Log.d(message);
        }
        
        private void CloseWindow()
        {
            if (App.MainWindow.Content is BatchSwapWindow window)
            {
                window.Close();
            }
        }
        
        private async Task<bool> ShowConfirmDialog(string message)
        {
            
            var box = MessageBoxManager.GetMessageBoxStandard("是否确认兑换", message, ButtonEnum.Ok, Icon.Info);
            await box.ShowWindowDialogAsync(App.MainWindow);
            return true;
        }

        // 生成随机金额的方法
        private void GenerateRandomAmounts()
        {
            if (AmountMin <= 0 || AmountMax <= 0)
            {
                Toast.Show(this, "金额必须大于0");
                return;
            }
            
            if (AmountMin > AmountMax)
            {
                Toast.Show(this, "最小金额不能大于最大金额");
                return;
            }
            
            var targetAccounts = RightAccounts.ToList();
            if (targetAccounts.Count == 0)
            {
                Toast.Show(this, "请先导入目标账号");
                return;
            }
            
            // 清空之前的随机金额
            RandomAmounts.Clear();
            
            // 创建随机数生成器
            var random = new Random();
            
            // 为每个账户生成随机金额
            double totalAmount = 0;
            for (int i = 0; i < targetAccounts.Count; i++)
            {
                // 生成最小值到最大值之间的随机数
                double randomAmount = AmountMin + (random.NextDouble() * (AmountMax - AmountMin));
                // 保留3位小数
                randomAmount = Math.Round(randomAmount, 4);
                
                RandomAmounts.Add(randomAmount);
                totalAmount += randomAmount;
                
                // 更新右侧账号的接收金额
                RightAccounts[i].ToAmount = randomAmount;
            }
            
            // 更新左侧账号的转出金额（如果有）
            if (LeftAccounts.Count > 0)
            {
                LeftAccounts[0].FromAmount = totalAmount;
            }
            
            // 更新总随机金额
            TotalRandomAmount = totalAmount;
            
            Log($"已生成随机金额，共 {targetAccounts.Count} 个账户，总金额 {totalAmount:F4} SOL");
            Log($"金额范围: {AmountMin:F4} - {AmountMax:F4} SOL");
        }

        // 添加更新多转多金额的方法
        private void UpdateManyToManyAmounts()
        {
            var sourceAccounts = LeftAccounts.Where(a => a.IsSelected).ToList();
            var targetAccounts = RightAccounts.ToList();
            
            // 确定实际转账对数
            int pairCount = Math.Min(sourceAccounts.Count, targetAccounts.Count);
            
            // 清空所有账户的转账金额
            foreach (var account in LeftAccounts)
            {
                account.FromAmount = 0;
            }
            
            foreach (var account in RightAccounts)
            {
                account.ToAmount = 0;
            }
            
            // 计算并更新转账金额
            for (int i = 0; i < pairCount; i++)
            {
                var sourceAccount = sourceAccounts[i];
                var targetAccount = targetAccounts[i];
                
                // 计算转账金额（账户余额的百分比）
                double transferAmount = sourceAccount.SolBalance * (ManyToManyPercent / 100.0);
                
                // 确保至少保留0.0011 SOL作为手续费和账号留存
                double minReserve = 0.0011;
                if (transferAmount > sourceAccount.SolBalance - minReserve)
                {
                    transferAmount = sourceAccount.SolBalance - minReserve;
                    if (transferAmount < 0)
                    {
                        transferAmount = 0;
                    }
                }
                
                // 更新转出和接收金额
                sourceAccount.FromAmount = Math.Round(transferAmount, 6);
                targetAccount.ToAmount = Math.Round(transferAmount, 6);
            }
            
            // 计算总转账金额
            double totalAmount = sourceAccounts.Take(pairCount).Sum(a => a.FromAmount);
            Log($"多转多模式：计划转出 {totalAmount:F6} SOL，转账比例 {ManyToManyPercent}%");
        }

        // 添加更新多转1金额的方法
        private void UpdateManyToOneAmounts()
        {
            var sourceAccounts = LeftAccounts.Where(a => a.IsSelected).ToList();
            var targetAccounts = RightAccounts.Where(a => a.IsSelected).ToList();
            
            // 清空所有来源账户的转账金额
            foreach (var account in LeftAccounts)
            {
                account.FromAmount = 0;
            }
            
            // 清空所有目标账户的接收金额
            foreach (var account in RightAccounts)
            {
                account.ToAmount = 0;
            }
            
            // 如果没有选中的来源账号或目标账号，直接返回
            if (sourceAccounts.Count == 0 || targetAccounts.Count == 0)
            {
                return;
            }
            
            // 确保只有一个目标账号
            var targetAccount = targetAccounts.First();
            double totalAmount = 0;
            
            // 计算并更新转账金额
            foreach (var sourceAccount in sourceAccounts)
            {
                // 计算转账金额（账户余额的百分比）
                double transferAmount = sourceAccount.SolBalance * (TransferPercent / 100.0);
                
                // 确保至少保留0.0011 SOL作为手续费和账号留存
                double minReserve = 0.0011;
                if (transferAmount > sourceAccount.SolBalance - minReserve)
                {
                    transferAmount = sourceAccount.SolBalance - minReserve;
                    if (transferAmount < 0)
                    {
                        transferAmount = 0;
                    }
                }
                
                // 更新转出金额
                sourceAccount.FromAmount = Math.Round(transferAmount, 6);
                totalAmount += transferAmount;
            }
            
            // 更新目标账号的接收金额
            targetAccount.ToAmount = Math.Round(totalAmount, 6);
            
            // 记录日志
            Log($"多转1模式：计划从 {sourceAccounts.Count} 个账号转出总计 {totalAmount:F6} SOL，转账比例 {TransferPercent}%");
        }
    
    }
}