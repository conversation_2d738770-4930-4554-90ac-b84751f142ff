using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading.Tasks;
using Avalonia.Controls;
using ChainTool.Models;
using ChainTool.Utils;
using ChainTool.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CsvHelper;
using System.Globalization;
using Solnet.Wallet;

namespace ChainTool.ViewModels
{
    public partial class BatchWalletViewModel : ViewModelBase
    {
        [ObservableProperty] private ObservableCollection<WalletModel> wallets = new();
        [ObservableProperty] private int walletCount = 10;

        public IRelayCommand CreateWalletsCommand { get; }
        public IRelayCommand ExportWalletsCommand { get; }

        public BatchWalletViewModel()
        {
            CreateWalletsCommand = new RelayCommand(CreateWallets);
            ExportWalletsCommand = new RelayCommand(async () => await ExportWallets());
        }

        private void CreateWallets()
        {
            if (WalletCount <= 0 || WalletCount > 1000)
            {
                Toast.Show(this, "请输入1-1000之间的数量");
                return;
            }

            Wallets.Clear();
            Log($"开始创建 {WalletCount} 个钱包...");

            for (int i = 0; i < WalletCount; i++)
            {
                try
                {
                    // 生成新钱包
                    var wallet = new Account();
                    
                    // 添加到列表
                    Wallets.Add(new WalletModel
                    {
                        Index = i + 1,
                        PublicKey = wallet.PublicKey.Key,
                        PrivateKey = wallet.PrivateKey.Key
                    });
                }
                catch (Exception ex)
                {
                    Log($"创建钱包失败: {ex.Message}");
                }
            }

            Log($"成功创建 {Wallets.Count} 个钱包");
            Toast.Show(this, $"成功创建 {Wallets.Count} 个钱包");
        }

        private async Task ExportWallets()
        {
            if (Wallets.Count == 0)
            {
                Toast.Show(this, "没有钱包可导出");
                return;
            }

            var dialog = new SaveFileDialog
            {
                Title = "导出钱包",
                Filters = { new FileDialogFilter { Name = "CSV 文件", Extensions = { "csv" } } },
                InitialFileName = $"wallets_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            string filePath = await dialog.ShowAsync(App.MainWindow);

            if (!string.IsNullOrEmpty(filePath))
            {
                try
                {
                    using (var writer = new StreamWriter(filePath))
                    using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                    {
                        // 写入标题行
                        csv.WriteField("publicKey");
                        csv.WriteField("privateKey");
                        csv.NextRecord();

                        // 写入数据
                        foreach (var wallet in Wallets)
                        {
                            csv.WriteField(wallet.PublicKey);
                            csv.WriteField(wallet.PrivateKey);
                            csv.NextRecord();
                        }
                    }

                    Log($"钱包已导出到: {filePath}");
                    Toast.Show(this, "钱包导出成功");
                }
                catch (Exception ex)
                {
                    Log($"导出钱包失败: {ex.Message}");
                    Toast.Show(this, $"导出失败: {ex.Message}");
                }
            }
        }

        private void Log(string message)
        {
            Shared.Log.d(message);
        }
    }

    public class WalletModel
    {
        public int Index { get; set; }
        public string PublicKey { get; set; }
        public string PrivateKey { get; set; }
    }
}