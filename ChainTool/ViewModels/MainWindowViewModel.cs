using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Threading;
using ChainTool.Models;
using ChainTool.Utils;
using ChainTool.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CsvHelper;
using DynamicData;
using ReactiveUI;
using Shared;
using Solnet.Programs;
using Solnet.Programs.PumpFunAmm;
using Solnet.Rpc.Builders;
using Solnet.Wallet;
using Wallet = ChainTool.Models.Wallet;

namespace ChainTool.ViewModels;

public partial class MainWindowViewModel : ViewModelBase {
    // 表单属性
    [ObservableProperty] private string tokenAddress = Config.TOKEN_Mint;
    [ObservableProperty] private string poolAddress = "";
    
    [ObservableProperty] private string ammConfigAddress = FilpFlopProgram.AmmConfig;
    [ObservableProperty] private string ammPoolAddress = FilpFlopProgram.AmmPool;
    
    [ObservableProperty] private string ownerAddress = "FYdn1FerT9sbCdv4ABz3nJxvTKNkBxkErXAGYWuqhfxC";
    [ObservableProperty] private string tragetAddress = "FYdn1FerT9sbCdv4ABz3nJxvTKNkBxkErXAGYWuqhfxC";
    [ObservableProperty] private string transferBalancePercent = "100";
    
    [ObservableProperty] private string poolSol;
    [ObservableProperty] private string poolToken;
    [ObservableProperty] private string price;
    [ObservableProperty] private double holdSol;
    [ObservableProperty] private double holdToken;
    [ObservableProperty] private double holdValue;
    
    [ObservableProperty] private int slipBuy = 100;
    [ObservableProperty] private int buyAmount = 50;
    [ObservableProperty] private int sellAmount = 50;

    // 新增自定义SOL数量属性
    [ObservableProperty] private double customBuySolAmount = 0.3d;
    [ObservableProperty] private double customSellSolAmount = 0.0d;
    
    [ObservableProperty] private int slipSell =100;
    
    [ObservableProperty] private double fee = 0.0002;
    
    [ObservableProperty] private bool mergeOrder;
    [ObservableProperty] private bool autoBuy;
    [ObservableProperty] private bool autoRefresh;
    [ObservableProperty] private bool ammListen;
    [ObservableProperty] private long refreshInterval = 1000;//刷新间隔
    // 表格数据
    [ObservableProperty] private  ObservableCollection<AccountModel> accounts = new();
    // 表格数据
    [ObservableProperty] private ObservableCollection<AmmMarket> poolList = new();
    
    [ObservableProperty] private AmmMarket selectedPool;
    [ObservableProperty] private Tuple<string,string> selectedPoolProgram;
    // 日志
    public ObservableCollection<string> Logs { get; } = new();

    // 命令
    public IRelayCommand QueryBalanceCommand { get; }
    public IRelayCommand ImportAccountCommand { get; }
    public IRelayCommand OneKeyBuyCommand { get; }
    public IRelayCommand OneKeySellCommand { get; }
    public IRelayCommand CheckPoolCommand { get; }
    public IRelayCommand ClearCommand { get; }
    public IRelayCommand AnalyzeMarketsCommand { get; }
    public IRelayCommand SettingCommand { get; }
    public IRelayCommand BatchSwapCommand { get; }
    public IRelayCommand CopyAllLogsCommand { get; }
    public IRelayCommand SaveLogsCommand { get; }
    public IRelayCommand SaveTokenAddressCommand { get; }
    public IRelayCommand ShowTokenListCommand { get; }
    public IRelayCommand SetBuyPercentCommand { get; } 
    public IRelayCommand SetSellPercentCommand { get; }
    public IRelayCommand BatchWalletCommand { get; }
    private IniReader _configIni;

    // 添加一个调度器用于定时刷新
    private DispatcherTimer _refreshTimer;

    // 添加保存的Token地址列表
    [ObservableProperty] private ObservableCollection<string> savedTokenAddresses = new();
    PumpfunAmmMigrationListener listener = new PumpfunAmmMigrationListener();
    // 统计数据
    [ObservableProperty] private double totalSolBuy = 0;
    [ObservableProperty] private double totalSolSell = 0;
    [ObservableProperty] private double totalSolNet = 0;
    [ObservableProperty] private double totalPoolSolWithNet = 0;

    // 更新统计数据的方法
    private void UpdateStatistics()
    {
        // 计算买入和卖出总量
        var list = TransactionHistory.getTotalSolBuyAndSell(TokenAddress);

        if (list==null||list.Count<0) {
            return;
        }
        foreach (AccountModel accountModel in Accounts) {
            accountModel.TradeCount = 0;
            accountModel.FbcRatio = accountModel.FbcBalance / 1000_000_000D;
            bool tryParse = Double.TryParse(Price, out double PriceValue);
            accountModel.FbcValue = accountModel.FbcBalance * PriceValue ;
            Log.d($"{accountModel.Address},FbcValue:{accountModel.FbcValue}");
        }
        
        
        TotalSolBuy = 0;
        TotalSolSell = 0;
        
        foreach (var item in list) {
            double.TryParse(item.solAmount, out double solSell);
            if (item.solAmount.Contains("-") ) {
                TotalSolBuy += solSell;
            } else {
                TotalSolSell +=  solSell;
            }

            AccountModel acc = Accounts.FirstOrDefault(a => a.Address == item.walletPublicKey);

            if (acc!=null) {
                acc.TradeCount++;
            }
        }
        

        
        // 计算净值
        TotalSolNet = TotalSolBuy + TotalSolSell;
        
        
        // 计算池子总量加净值
        double poolSolValue = 0;
        if (double.TryParse(PoolSol, out poolSolValue))
        {
            TotalPoolSolWithNet = poolSolValue+TotalSolNet;
        }
    }

    public MainWindowViewModel() {
        // 初始化IniReader
        string configPath = Path.Combine(Environment.CurrentDirectory, "Config", "app.ini");
        // 确保目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(configPath));
        _configIni = new IniReader(configPath);
        
        // 从配置文件加载上次使用的TokenAddress
        string savedTokenAddress = _configIni.ReadString("Tokens", "LastTokenAddress", "7dSve9rvX3SPp2ES2Dr3jdrYKu6fF7yLnafDnjGSpump");
        TokenAddress = savedTokenAddress;
        
        // 加载保存的Token地址列表
        LoadSavedTokenAddresses();
        
        // 初始化命令
        SaveTokenAddressCommand = new RelayCommand(SaveCurrentTokenAddress);
        ShowTokenListCommand = new RelayCommand(() => { });
        
        QueryBalanceCommand = new RelayCommand(async () => { await QueryBalance(); });
        ImportAccountCommand = new RelayCommand(async () => { await ImportAccount(); });
        OneKeyBuyCommand = new RelayCommand(async () =>  _=OneKeyBuy());
        OneKeySellCommand = new RelayCommand(async()=> _=OneKeySell());
        ClearCommand = new RelayCommand(()=> {
            Logs.Clear();
        });
        CheckPoolCommand = new RelayCommand(
            async () => {
                await getTokenMarketList();
               
            });
        AnalyzeMarketsCommand = new RelayCommand(async () => {
            if (selectedPoolProgram!=null) {
                // await AnalyzeTokenMarkets(poolToken,selectedPoolProgram.Item1);
                Toast.Show(this,"暂未实现");
            }

        }); // 新增命令
        SettingCommand = new RelayCommand(() => {
            // 打开设置窗口
            var settingsWindow = new SettingsWindow();
            settingsWindow.showAsChild();
        });
        BatchSwapCommand = new RelayCommand(() => {
            // 打开设置窗口
            var settingsWindow = new BatchSwapWindow();
            settingsWindow.showAsChild();
        });
        BatchWalletCommand = new RelayCommand(() => {
            // 打开批量创建钱包窗口
            var batchWalletWindow = new BatchWalletWindow();
            batchWalletWindow.showAsChild();
        });

        
            
        SetBuyPercentCommand = new RelayCommand<string>((string percentStr) => { BuyAmount = int.Parse(percentStr); });
        SetSellPercentCommand = new RelayCommand<string>((string percentStr) => { SellAmount = int.Parse(percentStr); });
    
        CopyAllLogsCommand = new RelayCommand(CopyAllLogs);
        SaveLogsCommand = new RelayCommand(async () => await SaveLogs());
        // 示例数据
        log("开始初始化数据...");
        var accounts = new ObservableCollection<AccountModel>();
        Accounts = accounts;
        log($"程序启动，账号数量：{Accounts.Count}");

        // 初始化刷新定时器
        _refreshTimer = new DispatcherTimer();
        _refreshTimer.Tick += async (s, e) => {
            await getMarkerDetail();
        };

        // 从配置文件加载刷新间隔
        string savedInterval = _configIni.ReadString("Settings", "RefreshInterval", "1000");
        if (long.TryParse(savedInterval, out long interval)) {
            RefreshInterval = interval;
        }
        
        
        listener.OnPoolCreated += (poolAddress, creator, baseMint, quoteMint) => {
            log($"代币创建: {poolAddress}");
        };

        listener.OnMigrationCompleted += (user, mint, mintAmount, solAmount, bondingCurve, pool) => {
            log($"外盘迁移: 代币:{mint}");
            log($"外盘迁移: 外盘:{pool}");
        };
        
        listener.MonitorTokenMigration(tokenAddress,poolAddress);
        
        
    }
    [RelayCommand]
    private async Task addAmmPool() {
        if (string.IsNullOrEmpty(AmmConfigAddress)||string.IsNullOrEmpty(AmmPoolAddress)) {
            Toast.Show(this,"尚未输入amm配置地址或池子地址");
            return;
        }
        if (string.IsNullOrEmpty(TokenAddress)) {
            Toast.Show(this,"尚未输入代币Token地址");
            return;
        }
        var filpFlopMarketPair  = await FilpFlopProgram.GetMarketPair(TokenAddress,AmmConfigAddress,AmmPoolAddress);
        this.PoolList.Add(filpFlopMarketPair.ammMarket);
    }
    private async Task QueryBalance() {
        log($"查询余额");
        List<Task<Tuple<double,double,double>>> list = new List<Task<Tuple<double,double,double>>>();
        foreach (AccountModel accountModel in Accounts) {
            list.Add(API.QueryBalanceWithRPC(TokenAddress,accountModel));
        }
        await Task.WhenAll(list);
        HoldSol = 0;
        HoldToken = 0;
        HoldValue = 0;
        foreach (var task in list) {
            HoldSol+=task.Result.Item1;
            HoldToken+=task.Result.Item2;
        }

        if (double.TryParse(Price,out double _price)) {
            HoldValue=HoldToken*_price;
        }

        // 更新统计数据
        UpdateStatistics();
    }

    private async Task ImportAccount() {
        log($"导入账号");
        var dialog = new OpenFileDialog { AllowMultiple = false, Filters = { new FileDialogFilter() { Name = "CSV 文件", Extensions = { "csv" } } } };
        var result = await dialog.ShowAsync(App.MainWindow);

        if (result != null && result.Length > 0) {
            using (var reader = new StreamReader(result[0]))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture)) {
                var records = csv.GetRecords<Wallet>().ToList();
                Accounts.Clear();
                var address = records.Select<Wallet, AccountModel>(
                    w => { return new AccountModel() { Address = w.publicKey, PrivateAddress = w.privateKey, }; });

                foreach (var result1 in address) {
                    if (string.IsNullOrEmpty(result1.PrivateAddress)||string.IsNullOrEmpty(result1.Address)) {
                        continue;
                    }
                    Accounts.Add(result1);
                }
            }
        }
        log("程序启动"+Accounts.Count);
    }

    private async Task OneKeyBuy() {
        if(string.IsNullOrEmpty(Price) ||"0"==Price|| !double.TryParse(Price,out double price)) {
            Toast.Show(this,"价格格式错误");
            return ;
        }

        if (Accounts.Count<=0) {
            Toast.Show(this, "尚未导入账号");
            return;
        }
        
        
        if (SelectedPool?.programID == Config.Program_PumpFun) {
            PumpFunTransaction transaction = new PumpFunTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                //根据设定的要买入的sol数量, 依据代币价格,计算需要买入的代币数量
                var task = transaction.buy(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalBuyTokenAmount(accountModel, price), price, SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"PumpFun 买入成功: {task.Item2.Address}");
                } else {
                    log($"PumpFun 买入失败: {task.Item2.Address}");
                }
            }
        }else if(SelectedPool?.programID== PumpfunAmmTransaction.ProgramId) {
            PumpfunAmmTransaction transaction = new PumpfunAmmTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.buy(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalBuyTokenAmount(accountModel, price) , price,SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($" PumpfunAmm 买入成功: {task.Item2.Address}");
                } else {
                    log($" PumpfunAmm 买入失败: {task.Item2.Address}");
                }
            }
        }else if(SelectedPool?.programID== BonkBoundingCurveHelper.ProgramId) {
            BonkBoudingCurveTransaction transaction = new BonkBoudingCurveTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.buy(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalBuyTokenAmount(accountModel, price) , price,SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"BonkBounding 买入成功: {task.Item2.Address}");
                } else {
                    log($"BonkBounding 买入失败: {task.Item2.Address}");
                }
            }
        }
        else if (SelectedPool?.programName?.Contains("FlipFlopAmm")??false) {
            FilpFlopCpmmTransaction transaction = new FilpFlopCpmmTransaction();
            var taskList = new List<Tuple<Task<bool>, AccountModel>>();

            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.buy(
                    accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalBuyTokenAmount(accountModel, price), price
                  , SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>, AccountModel>(task, accountModel));
            }

            await Task.WhenAll(taskList.Select(t => t.Item1));

            foreach (var task in taskList) {
                if (task.Item1.Result) { log($"FlipFlopAmm 买入成功: {task.Item2.Address}"); } 
                else { log($"FlipFlopAmm 买入失败: {task.Item2.Address}"); }
            }
        }
        else if(SelectedPool?.programID== BonkCpmmHelper.ProgramId) {
            BonkCpmmTransaction transaction = new BonkCpmmTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.buy(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalBuyTokenAmount(accountModel, price) , price,SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"BonkCpmm 买入成功: {task.Item2.Address}");
                } else {
                    log($"BonkCpmm 买入失败: {task.Item2.Address}");
                }
            }
        } else {
            
            
            Toast.Show(this,"暂未实现");
        }

        // 更新统计数据
        UpdateStatistics();
    }

    private double getFinalBuyTokenAmount(AccountModel accountModel, double price) {
        // 如果自定义SOL数量大于0，使用自定义数量；否则使用百分比模式
        if (CustomBuySolAmount > 0) {
            return CustomBuySolAmount / price;
        } else {
            var totalAmount = (BuyAmount / 100D * accountModel.SolBalance) / price;
            return totalAmount;
        }
    }

    private async Task  OneKeySell() {
        if( !double.TryParse(Price,out double price)) {
            Toast.Show(this,"价格格式错误");
            return ;
        }

        if (Accounts.Count<=0) {
            Toast.Show(this, "尚未导入账号");
            return;
        }
        if (SelectedPool?.programID == Config.Program_PumpFun) {
            PumpFunTransaction transaction = new PumpFunTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.sell(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalSellTokenAmout(accountModel, price), price, SlipSell, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"卖出成功: {task.Item2.Address}");
                } else {
                    log($"卖出失败: {task.Item2.Address}");
                }
            }
        }else if(SelectedPool?.programID== PumpfunAmmTransaction.ProgramId) {
            PumpfunAmmTransaction transaction = new PumpfunAmmTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.sell(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalSellTokenAmout(accountModel, price), price, SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"卖出成功: {task.Item2.Address}");
                } else {
                    log($"卖出失败: {task.Item2.Address}");
                }
            }
        }else if(SelectedPool?.programID== BonkBoundingCurveHelper.ProgramId) {
            BonkBoudingCurveTransaction transaction = new BonkBoudingCurveTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.sell(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalSellTokenAmout(accountModel, price), price, SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"卖出成功: {task.Item2.Address}");
                } else {
                    log($"卖出失败: {task.Item2.Address}");
                }
            }
        } 
        else if (SelectedPool?.programName?.Contains("FlipFlopAmm")??false) {
            FilpFlopCpmmTransaction transaction = new FilpFlopCpmmTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.sell(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalSellTokenAmout(accountModel, price), price, SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"卖出成功: {task.Item2.Address}");
                } else {
                    log($"卖出失败: {task.Item2.Address}");
                }
            }
        } 
        else if(SelectedPool?.programID== BonkCpmmHelper.ProgramId) {
            BonkCpmmTransaction transaction = new BonkCpmmTransaction();
            var taskList = new List<Tuple<Task<bool>,AccountModel>>();
            foreach (AccountModel accountModel in Accounts) {
                var task = transaction.sell(accountModel.Address, accountModel.PrivateAddress, TokenAddress, (ulong)getFinalSellTokenAmout(accountModel, price), price, SlipBuy, Fee);
                taskList.Add(new Tuple<Task<bool>,AccountModel>(task,accountModel));
            }
            await Task.WhenAll(taskList.Select(t => t.Item1));
            foreach (var task in taskList) {
                if (task.Item1.Result) {
                    log($"卖出成功: {task.Item2.Address}");
                } else {
                    log($"卖出失败: {task.Item2.Address}");
                }
            }
        } 

        else {
            Toast.Show(this,"暂未实现");
        }

        // 更新统计数据
        UpdateStatistics();
    }

    private double getFinalSellTokenAmout(AccountModel accountModel, double price) {
        // 如果自定义SOL数量大于0，使用自定义数量；否则使用百分比模式
        if (CustomSellSolAmount > 0) {
            // 根据自定义SOL数量和当前价格计算需要卖出的token数量
            return CustomSellSolAmount / price;
        } else {
            return accountModel.FbcBalance * (SellAmount / 100D);
        }
    }

    public void log(string msg) {
        Logs.Add($"[{DateTime.Now:HH:mm:ss}] {msg}");
        // 延迟一下以确保UI更新完成
        App.MainWindow?.postDelay(() => {
            if (App.MainWindow?.FindControl<ListBox>("LogListBox") is { } listBox) {
                listBox.ScrollIntoView(Logs[^1]);
            }
        },50);
    }

    partial void OnTokenAddressChanged(string? oldValue, string newValue) {
        // 保存新的TokenAddress到配置文件
        if (!string.IsNullOrWhiteSpace(newValue)) {
            _configIni.Write("Tokens", "LastTokenAddress", newValue);
            _configIni.Save();
            log($"已保存当前Token地址: {newValue}");
        }
        
        _ = getTokenMarketList();
    }

    partial void OnSelectedPoolChanged(AmmMarket oldValue, AmmMarket newValue) {
        _= getMarkerDetail();
    }

    private async Task getMarkerDetail() {
        if (string.IsNullOrWhiteSpace(SelectedPool?.pool)) {
            Toast.Show(this,"尚未选择池子");
            return;
        }
        log($"获取池子信息: {SelectedPool.pool}");
        
        AmmMarket rsp = await API.MARKETS_DETAILED(SelectedPool);
        
        if (rsp==null||string.IsNullOrEmpty(rsp.price)) {
            Toast.Show(this,$"获取池子信息失败: {SelectedPool.pool}");
        }

        PoolToken = rsp?.tokenAmount?.ToString() ?? "0";
        log($"pool token2:{PoolToken}");

        PoolSol = rsp?.solAmount?.ToString() ?? "0";
        log($"pool Token1:{PoolSol}");

        Price = rsp?.price?.ToString() ?? "0";
        log($"pool Token1:{PoolSol}");

        if (PoolToken!="0") {
            //更新交易统计数据
            UpdateStatistics();
        }
    }

    private async Task getTokenMarketList() {
        if (string.IsNullOrWhiteSpace(TokenAddress)) {
            Toast.Show(this,"尚未设置Token地址");
            return ;
        }
        this.PoolList.Clear();
        this.SelectedPool = null;
        
        //PumpFun Amm外池地址
        this.PoolAddress =  PumpfunAmmMigrationListener.GetPoolForToken(TokenAddress);
        log($"外池地址: {PoolAddress}");
        
        //计算pumpfun,内外池的地址
        var rsp = await API.TOKEN_MARKETS(TokenAddress);
        var innerPool = rsp.innerMarket.pool;
        var ammPool = rsp.ammMarket.pool;
        log($"内池地址: {innerPool}");
        log($"外池地址: {ammPool}");
        
        
        this.PoolList.Add(rsp.innerMarket);
        this.PoolList.Add(rsp.ammMarket);
        
        
        
        var bonkMarketPair  = await BonkFunProgram.GetMarketPair(TokenAddress);
        
        
        this.PoolList.Add(bonkMarketPair.innerMarket);
        this.PoolList.Add(bonkMarketPair.ammMarket);
        
        log($"获取池子数量:{this.PoolList.Count}");
        
        foreach (var data in this.PoolList) {
            if(data.programID.Equals(Config.Program_PumpFun)) {
                SelectedPool = data;   
            }
        }
    }


    // 监听AutoRefresh属性的变化
    partial void OnAutoRefreshChanged(bool oldValue, bool newValue) {
        if (newValue) {
            // 启动定时器
            _refreshTimer.Interval = TimeSpan.FromMilliseconds(RefreshInterval);
            _refreshTimer.Start();
            log($"已启动自动刷新，间隔: {RefreshInterval}毫秒");
        }
        else {
            // 停止定时器
            _refreshTimer.Stop();
            log("已停止自动刷新");
        }
    }
    // 监听AutoRefresh属性的变化
    partial void OnAmmListenChanged(bool oldValue, bool newValue) {
        if (newValue) {
            // 启动定时器
            listener.mintAddress = tokenAddress;
            listener.StartListening();
            log("已启动AMM监听");
        } else {
            // 停止定时器
            listener.StopListening();
            log("已停止自动刷新");
        }
    }

    // 监听RefreshInterval属性的变化
    partial void OnRefreshIntervalChanged(long oldValue, long newValue) {
        // 更新定时器间隔
        _refreshTimer.Interval = TimeSpan.FromMilliseconds(newValue);
        
        // 如果定时器正在运行，重新启动它以应用新的间隔
        if (AutoRefresh) {
            _refreshTimer.Stop();
            _refreshTimer.Start();
            log($"已更新自动刷新间隔: {newValue}毫秒");
        }
        
        // 保存到配置文件
        _configIni.Write("Settings", "RefreshInterval", newValue.ToString());
        _configIni.Save();
    }

    // 在析构函数或Dispose方法中停止定时器
    ~MainWindowViewModel() {
        _refreshTimer?.Stop();
    }

    // 复制所有日志到剪贴板
    private async void CopyAllLogs() {
        try {
            if (Logs.Count == 0) {
                Toast.Show(this, "没有日志可复制");
                return;
            }
            
            // 将所有日志合并为一个字符串
            string allLogs = string.Join(Environment.NewLine, Logs);
            
            // 复制到剪贴板
            await TopLevel.GetTopLevel(App.MainWindow).Clipboard.SetTextAsync(allLogs);
            
            Toast.Show(this, "已复制所有日志到剪贴板");
        }
        catch (Exception ex) {
            log($"复制日志失败: {ex.Message}");
        }
    }

    // 保存日志到文件
    private async Task SaveLogs() {
        try {
            if (Logs.Count == 0) {
                Toast.Show(this, "没有日志可保存");
                return;
            }
            
            // 创建保存文件对话框
            var dialog = new SaveFileDialog {
                Title = "保存日志",
                Filters = { new FileDialogFilter { Name = "日志文件", Extensions = { "log", "txt" } } },
                InitialFileName = $"ChainTool_Log_{DateTime.Now:yyyyMMdd_HHmmss}.log"
            };
            
            // 显示对话框
            string filePath = await dialog.ShowAsync(App.MainWindow);
            
            if (!string.IsNullOrEmpty(filePath)) {
                // 将所有日志合并为一个字符串
                string allLogs = string.Join(Environment.NewLine, Logs);
                
                // 保存到文件
                await File.WriteAllTextAsync(filePath, allLogs);
                
                Toast.Show(this, $"日志已保存到: {filePath}");
            }
        }
        catch (Exception ex) {
            log($"保存日志失败: {ex.Message}");
        }
    }

    // 加载保存的Token地址列表
    private void LoadSavedTokenAddresses() {
        try {
            // 从配置文件中读取保存的Token地址
            int count = _configIni.ReadInt32("Tokens", "Count", 0);
            SavedTokenAddresses.Clear();
            
            for (int i = 0; i < count; i++) {
                string address = _configIni.ReadString("Tokens", $"Address{i}", "");
                if (!string.IsNullOrWhiteSpace(address) && !SavedTokenAddresses.Contains(address)) {
                    SavedTokenAddresses.Add(address);
                }
            }
            
            // 确保当前Token地址在列表中
            if (!string.IsNullOrWhiteSpace(TokenAddress) && !SavedTokenAddresses.Contains(TokenAddress)) {
                SavedTokenAddresses.Add(TokenAddress);
            }
            
            log($"已加载{SavedTokenAddresses.Count}个保存的Token地址");
        } catch (Exception ex) {
            log($"加载Token地址列表失败: {ex.Message}");
        }
    }

    // 保存当前Token地址到列表
    private void SaveCurrentTokenAddress() {
        try {
            if (string.IsNullOrWhiteSpace(TokenAddress)) {
                Toast.Show(this, "Token地址不能为空");
                return;
            }
            
            // 如果地址已存在，则不重复添加
            if (!SavedTokenAddresses.Contains(TokenAddress)) {
                SavedTokenAddresses.Add(TokenAddress);
                log($"已将Token地址添加到收藏: {TokenAddress}");
            } else {
                log($"Token地址已在收藏列表中: {TokenAddress}");
            }
            
            // 保存到配置文件
            _configIni.Write("Tokens", "Count", SavedTokenAddresses.Count.ToString());
            for (int i = 0; i < SavedTokenAddresses.Count; i++) {
                _configIni.Write("Tokens", $"Address{i}", SavedTokenAddresses[i]);
            }
            _configIni.Save();
            
            Toast.Show(this, "Token地址已收藏");
        } catch (Exception ex) {
            log($"保存Token地址失败: {ex.Message}");
        }
    }
}
