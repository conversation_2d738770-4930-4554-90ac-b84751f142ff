<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Solana操盘"
        x:Class="ChainTool.Views.MainWindow"
        Width="1400" Height="700"
        xmlns:vm="using:ChainTool.ViewModels"
        x:DataType="vm:MainWindowViewModel">
    
    <Window.DataContext>
        <vm:MainWindowViewModel />
    </Window.DataContext>
    
    
    <Grid ColumnDefinitions="1000,*" RowDefinitions="*">
        <HeaderedContentControl Grid.Column="0" Header="批量交易" Margin="4,10,0,10" >
            <!-- 左侧表单 -->
            <Grid Grid.Row="0"
                  Grid.RowDefinitions="Auto,*,Auto">
                <StackPanel Grid.Row="0" Margin="10,10,5,10" Spacing="4">


                    <!-- Token地址 -->
                    <StackPanel Orientation="Horizontal" Spacing="6">
                        <TextBlock Text="" VerticalAlignment="Center" />
                        <AutoCompleteBox Width="360" FontSize="12"
                                        ItemsSource="{Binding SavedTokenAddresses}"
                                        Text="{Binding TokenAddress, Mode=TwoWay}"
                                        Watermark="输入Token地址" />
                        <Button Content="收藏" Command="{Binding SaveTokenAddressCommand}" Width="60" />
                        
                        <TextBlock Text="Market列表" VerticalAlignment="Center" />
                        <ComboBox Width="400" FontSize="12"
                                  ItemsSource="{Binding PoolList}"
                                  SelectedItem="{Binding SelectedPool, Mode=TwoWay}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <TextBlock.Text>
                                            <MultiBinding StringFormat="{}{0} ({1})">
                                                <Binding Path="pool" />
                                                <Binding Path="programName" />
                                            </MultiBinding>
                                        </TextBlock.Text>
                                    </TextBlock>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        <Button Content="刷新" Command="{Binding CheckPoolCommand}" />
                    </StackPanel>
                    <HeaderedContentControl Grid.Column="0" Header="自定义AmmMarket" Margin="4,10,0,10" >
                    <StackPanel Orientation="Horizontal" Spacing="6">
                        <TextBlock Text="Config" VerticalAlignment="Center" />
                        <TextBox Text="{Binding AmmConfigAddress}" Watermark="AmmConfig" Width="400" VerticalAlignment="Center"  />
                        <TextBlock Text="Pool" VerticalAlignment="Center" />
                        <TextBox Text="{Binding AmmPoolAddress}" Watermark="AmmPool" Width="400" VerticalAlignment="Center" />
                        <Button Content="添加" Command="{Binding addAmmPoolCommand}" />
                    </StackPanel>
                        </HeaderedContentControl>
                    <!-- 池子SOL/池子Token/单价/持有SOL/持有Token/持有价值 -->
                    <Grid Margin="0,4,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="320" />
                            <ColumnDefinition Width="180" />
                            <ColumnDefinition Width="180" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="36" />
                        </Grid.RowDefinitions>
                        <!-- 第一行：标题 -->
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="池子SOL" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="4,0,0,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="池子Token" VerticalAlignment="Center" HorizontalAlignment="Left"  Margin="4,0,0,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="单价" VerticalAlignment="Center" HorizontalAlignment="Left"  Margin="4,0,0,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="刷新间隔(ms)" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="4,0,0,0" />
                        
                        <!-- 第二行：输入框 -->
                        <TextBox Grid.Row="1" Grid.Column="0" Text="{Binding PoolSol, Mode=TwoWay}" Margin="4,0" />
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding PoolToken, Mode=TwoWay}" Margin="4,0" />
                        <TextBox Grid.Row="1" Grid.Column="2" Text="{Binding Price, Mode=TwoWay}" Margin="4,0" />
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding RefreshInterval, Mode=TwoWay}" Margin="4,0" />
                        <CheckBox Grid.Row="1" Grid.Column="4" Content="自动刷新" IsChecked="{Binding AutoRefresh, Mode=TwoWay}" />
                        <CheckBox Grid.Row="1" Grid.Column="5" Content="监听AMM" IsChecked="{Binding AmmListen, Mode=TwoWay}" />
                        
                    </Grid>
                    <StackPanel Orientation="Horizontal" Spacing="6" Margin="0,12,0,0">
                        <TextBlock Text="小费" Width="60" VerticalAlignment="Center" />
                        <NumericUpDown Grid.Row="1" Grid.Column="1"  Width="260"
                                       Increment="0.001"
                                       Value="{Binding Fee, Mode=TwoWay}"
                                       Minimum="0.00000"  Maximum="1" Margin="0,10,0,0"/>
                        <TextBlock Text="优先交易费,单位sol(1sol=1_000_000_000lamp)"  VerticalAlignment="Center" />
                    </StackPanel>
                    <!-- 第一组参数 -->
                    <StackPanel Orientation="Horizontal" Spacing="6" Margin="0,12,0,0">
                        <!-- <CheckBox Content="开启防夹" IsChecked="{Binding AutoBuy, Mode=TwoWay}" /> -->
                        <TextBlock Text="滑点(买)" Width="60" VerticalAlignment="Center" />
                        <NumericUpDown Grid.Row="1" Grid.Column="1" 
                                       Watermark="百分比"
                                       Value="{Binding SlipBuy, Mode=TwoWay}"
                                       Minimum="1"   Increment="1" Maximum="100000" Margin="0,10,0,0"  Width="260"/>
                        <TextBlock Text="买入百分比" Width="80" VerticalAlignment="Center" />
                        <Button Content="80%" Command="{Binding SetBuyPercentCommand}" CommandParameter="80" Width="80" />
                        <Button Content="50%" Command="{Binding SetBuyPercentCommand}" CommandParameter="50" Width="80" />
                        <Button Content="25%" Command="{Binding SetBuyPercentCommand}" CommandParameter="25" Width="80" />
                        <TextBox  Text="{Binding BuyAmount, Mode=TwoWay}" Margin="4,0" />
                        <TextBox  Text="{Binding BuySolAmount, Mode=TwoWay}" Margin="4,0" />
                        <Button Content="一键买入" Command="{Binding OneKeyBuyCommand}" Width="100" />

                    </StackPanel>
                    <!-- 第二组参数 -->
                    <StackPanel Orientation="Horizontal" Spacing="6" Margin="0,4,0,0">
                        <!-- <CheckBox Content="合并交易" IsChecked="{Binding MergeOrder, Mode=TwoWay}" /> -->
                        <TextBlock Text="滑点(卖)" Width="60" VerticalAlignment="Center" />
                        <NumericUpDown Grid.Row="1" Grid.Column="1" 
                                       Watermark="百分比"
                                       Value="{Binding SlipSell, Mode=TwoWay}"
                                       Minimum="1" Increment="1" Maximum="100000" Margin="0,10,0,0"  Width="260"/>
                        <TextBlock Text="卖出百分比" Width="100" VerticalAlignment="Center" />
                        <Button Content="100%" Command="{Binding SetSellPercentCommand}" CommandParameter="100" Width="80" />
                        <Button Content="50%" Command="{Binding SetSellPercentCommand}"  CommandParameter="50"  Width="80" />
                        <Button Content="25%" Command="{Binding SetSellPercentCommand}"  CommandParameter="25"  Width="80" />
                        <TextBox  Text="{Binding SellAmount, Mode=TwoWay}" Margin="4,0" />
                        <TextBox  Text="{Binding SellSolAmount, Mode=TwoWay}" Margin="4,0" />
                        <Button Content="一键卖出" Command="{Binding OneKeySellCommand}" Width="100" />

                    </StackPanel>
                    <!-- 批量账号按钮 -->
                    <Grid Margin="0,4,0,0" ColumnDefinitions="*,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                        <TextBlock Text="账号列表" FontWeight="Bold" Margin="0,10,0,0" Grid.Column="0" VerticalAlignment="Center" />
                        <TextBlock Grid.Column="1" Text="持有SOL" VerticalAlignment="Center" HorizontalAlignment="Left" />
                        <TextBox Grid.Column="2" Text="{Binding HoldSol, Mode=TwoWay}" Margin="4,0" />
                        <TextBlock Grid.Column="3" Text="持有Token" VerticalAlignment="Center" HorizontalAlignment="Left" />
                        <TextBox Grid.Column="4" Width="200" Text="{Binding HoldToken, Mode=TwoWay}" Margin="4,0" />
                        <TextBlock Grid.Column="5" Text="持有价值" VerticalAlignment="Center" HorizontalAlignment="Left" />
                        <TextBox Grid.Column="6" Text="{Binding HoldValue, Mode=TwoWay ,StringFormat=F6}" Margin="4,0" />
                        
                        <Button Grid.Column="7" Content="导入账号" Command="{Binding ImportAccountCommand}" Width="100" Margin="0,4,0,0"/>
                        <Button Grid.Column="8" Content="查询余额" Command="{Binding QueryBalanceCommand}" Width="100" Margin="0,4,0,0"  />
                    </Grid>

                </StackPanel>

                <!-- 账号列表 -->
                <DataGrid Grid.Row="1" Margin="4"
                          ItemsSource="{Binding Accounts}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          BorderThickness="1"
                          BorderBrush="Gray">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="钱包" Binding="{Binding Address}" Width="*" FontSize="10"/>
                        <DataGridTextColumn Header="SOL余额" Binding="{Binding SolBalance}" Width="100" />
                        <DataGridTextColumn Header="Token余额" Binding="{Binding FbcBalance}" Width="120" />
                        <DataGridTextColumn Header="Token比例" Binding="{Binding FbcRatio ,StringFormat=F4}" Width="120" />
                        <DataGridTextColumn Header="Token价值" Binding="{Binding FbcValue ,StringFormat=F6}" Width="120" />
                        <DataGridTextColumn Header="交易次数" Binding="{Binding TradeCount}" Width="100" />
                        <DataGridTextColumn Header="最后交易" Binding="{Binding LastTrade}" Width="150" FontSize="12"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 统计数据行 -->
                <Grid Grid.Row="2" Margin="4,8,4,4">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="SOL买入总量:" VerticalAlignment="Center" Margin="4,0"/>
                    <TextBox Grid.Column="1" Text="{Binding TotalSolBuy, StringFormat=F6}" IsReadOnly="True" Margin="4,0"/>
                    
                    <TextBlock Grid.Column="2" Text="SOL卖出总量:" VerticalAlignment="Center" Margin="4,0"/>
                    <TextBox Grid.Column="3" Text="{Binding TotalSolSell, StringFormat=F6}" IsReadOnly="True" Margin="4,0"/>
                    
                    <TextBlock Grid.Column="4" Text="SOL净值:" VerticalAlignment="Center" Margin="4,0"/>
                    <TextBox Grid.Column="5" Text="{Binding TotalSolNet, StringFormat=F6}" IsReadOnly="True" Margin="4,0"/>
                    
                    <TextBlock Grid.Column="6" Text="池子SOL+净值:" VerticalAlignment="Center" Margin="4,0"/>
                    <TextBox Grid.Column="7" Text="{Binding TotalPoolSolWithNet, StringFormat=F6}" IsReadOnly="True" Margin="4,0"/>
                </Grid>
                
                
                
            </Grid>
        </HeaderedContentControl>
        <!-- 右侧日志 -->
        <HeaderedContentControl Grid.Column="1" Margin="5,10,10,10" Header="操作日志">
            <Grid RowDefinitions="*,Auto" >
                <ListBox Grid.Row="0" ItemsSource="{Binding Logs}"
                         FontSize="12"
                         Name="LogListBox"
                         VerticalAlignment="Stretch"
                         ScrollViewer.VerticalScrollBarVisibility="Auto"
                         ScrollViewer.HorizontalScrollBarVisibility="Auto"
                         Margin="0,4,0,4">
                    <ListBox.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="清空日志" Command="{Binding ClearCommand}" />
                            <MenuItem Header="复制所有日志" Command="{Binding CopyAllLogsCommand}" />
                            <MenuItem Header="保存日志到文件" Command="{Binding SaveLogsCommand}" />
                        </ContextMenu>
                    </ListBox.ContextMenu>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"
                                       MinHeight="15"
                                       TextWrapping="NoWrap"
                                       FontSize="12"
                                       Margin="0,0,0,0"
                                       Padding="2,0,2,0" />
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                    <ListBox.Styles>
                        <Style Selector="ListBoxItem">
                            <Setter Property="Padding" Value="0" />
                            <Setter Property="Margin" Value="0" />
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        </Style>
                    </ListBox.Styles>
                </ListBox>
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="Clear" Command="{Binding ClearCommand}" FontWeight="Bold" FontSize="14" />
                    <Button Content="创建钱包"  Command="{Binding BatchWalletCommand}" FontWeight="Bold" FontSize="14" HorizontalAlignment="Right" />
                    <Button Content="批量转账"  Command="{Binding BatchSwapCommand}" FontWeight="Bold" FontSize="14" HorizontalAlignment="Right" />
                    <Button Content="设置"  Command="{Binding SettingCommand}" FontWeight="Bold" FontSize="14" HorizontalAlignment="Right" />
                </StackPanel>
            </Grid>
        </HeaderedContentControl>

    </Grid>
</Window>
