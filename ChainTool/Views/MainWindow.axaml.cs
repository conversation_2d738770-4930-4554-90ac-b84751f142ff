using System;
using Avalonia.Controls;
using ChainTool.ViewModels;
using Shared;

namespace ChainTool.Views;

public partial class MainWindow : BaseWindow ,ILogger{
    MainWindowViewModel viewModel;
    public MainWindow() {
        Log.loggers.Add(this);
        postDelay(() => {
            InitializeComponent();
            viewModel = DataContext as MainWindowViewModel;
        });
    }

    protected override void OnClosed(EventArgs e) {
        base.OnClosed(e);
        Log.closeAndFlush();
    }

    public void WriteLine(LoggerEntry m) {
        string level = m.logLevel switch {
            LogLevel.Information => "I", LogLevel.Warning => "W"
          , LogLevel.Error => "E", LogLevel.Debug => "D", _ => "F",
        };
        viewModel?.log($"[{level}][{m.time:MM/dd HH:mm:ss:fff}]{m.message}");
    }
    public void Flush() {
    }
}
