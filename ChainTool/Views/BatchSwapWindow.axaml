<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:ChainTool.ViewModels"
        xmlns:viewModels="clr-namespace:ChainTool.ViewModels"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
        x:Class="ChainTool.Views.BatchSwapWindow"
        Title="批量转账/兑换"
        Width="900" Height="700"
        WindowStartupLocation="CenterOwner"
        x:DataType="viewModels:BatchSwapViewModel">

    <Window.DataContext>
        <vm:BatchSwapViewModel />
    </Window.DataContext>

    <Grid Margin="20" RowDefinitions="Auto,Auto,*,Auto,Auto">
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="批量转账/兑换" FontWeight="Bold" FontSize="18" Margin="0,0,0,15"/>
        
        <!-- 转账方式选择 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,15" Spacing="15">
            <RadioButton GroupName="TransferMode" Content="1转多" 
                         IsChecked="{Binding IsOneToMany}" />
            <RadioButton GroupName="TransferMode" Content="多转多" 
                         IsChecked="{Binding IsManyToMany}" />
            <RadioButton GroupName="TransferMode" Content="多转1" 
                         IsChecked="{Binding IsManyToOne}" />
            <RadioButton GroupName="TransferMode" Content="批量兑换" 
                         IsChecked="{Binding IsExchange}" />
            <RadioButton GroupName="TransferMode" Content="代币转帐" 
                         IsChecked="{Binding IsTokenSwap}" />
        </StackPanel>
        
        <!-- 账号列表区域 -->
        <Grid Grid.Row="2" ColumnDefinitions="*,Auto,*">
            <!-- 左侧账号列表 -->
            <Grid Grid.Column="0" RowDefinitions="Auto,*,Auto">
                <TextBlock Grid.Row="0" Text="{Binding LeftListTitle}" 
                           FontWeight="Bold" Margin="0,0,0,5"/>
                
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding LeftAccounts}"
                          AutoGenerateColumns="False"
                          IsReadOnly="False"
                          GridLinesVisibility="All"
                          BorderThickness="1"
                          BorderBrush="Gray"
                          >
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="钱包地址" Binding="{Binding Address}" Width="*" FontSize="10"/>
                        <DataGridTextColumn Header="SOL余额" Binding="{Binding SolBalance}" Width="100" />
                        <DataGridTextColumn Header="USDC余额" Binding="{Binding UsdcBalance}" Width="100" />
                        <DataGridTextColumn Header="转出金额" Binding="{Binding FromAmount}" Width="100" />
                    </DataGrid.Columns>
                </DataGrid>
                
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,5,0,0" Spacing="5">
                    <Button Content="导入账号" Command="{Binding ImportLeftAccountsCommand}" />
                    <Button Content="查询余额" Command="{Binding QueryLeftBalanceCommand}" />
                </StackPanel>
            </Grid>
            
            <!-- 中间箭头 -->
            <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10,0">
                <TextBlock Text="→" FontSize="24" HorizontalAlignment="Center" />
                <TextBlock Text="{Binding TransferDirectionText}" HorizontalAlignment="Center" Margin="0,5,0,0" />
            </StackPanel>
            
            <!-- 右侧账号列表 -->
            <Grid Grid.Column="2" RowDefinitions="Auto,*,Auto">
                <TextBlock Grid.Row="0" Text="{Binding RightListTitle}" 
                           FontWeight="Bold" Margin="0,0,0,5"/>
                
                <DataGrid Grid.Row="1" 
                          ItemsSource="{Binding RightAccounts}"
                          AutoGenerateColumns="False"
                          IsReadOnly="False"
                          GridLinesVisibility="All"
                          BorderThickness="1"
                          BorderBrush="Gray">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="钱包地址" Binding="{Binding Address}" Width="*" FontSize="10"/>
                        <DataGridTextColumn Header="SOL余额" Binding="{Binding SolBalance}" Width="100" />
                        <DataGridTextColumn Header="USDC余额" Binding="{Binding UsdcBalance}" Width="100" />
                        <DataGridTextColumn Header="接收金额" Binding="{Binding ToAmount}" Width="100" />
                    </DataGrid.Columns>
                </DataGrid>
                
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,5,0,0" Spacing="5">
                    <Button Content="导入账号" Command="{Binding ImportRightAccountsCommand}" />
                    <Button Content="查询余额" Command="{Binding QueryRightBalanceCommand}" />
                </StackPanel>
            </Grid>
        </Grid>
        
        <!-- 转账参数设置 -->
        <Grid Grid.Row="3" Margin="0,15,0,0" RowDefinitions="Auto,Auto" ColumnDefinitions="Auto,*,Auto,Auto">
            <!-- 1转多模式参数 -->
            <StackPanel Grid.Row="0" Grid.ColumnSpan="4" 
                        IsVisible="{Binding IsOneToMany}"
                        Orientation="Horizontal" Spacing="10">
                <TextBlock Text="每个账户转入金额:" VerticalAlignment="Center" />
                <NumericUpDown Value="{Binding AmountMin}" Minimum="0.0001" Increment="0.0001" FormatString="F4" Width="150" Watermark="最小值" />
                <NumericUpDown Value="{Binding AmountMax}" Minimum="0.0001" Increment="0.0001" FormatString="F4" Width="150" Watermark="最大值" />
                <TextBlock Text="SOL" VerticalAlignment="Center" />
                
                <Button Content="随机金额" Command="{Binding RandomAmountCommand}" />
                <TextBlock Text="{Binding TotalRandomAmount}" VerticalAlignment="Center" />
            </StackPanel>
            
            <!-- 多转多模式参数 -->
            <StackPanel Grid.Row="0" Grid.ColumnSpan="4" 
                        IsVisible="{Binding IsManyToMany}"
                        Orientation="Horizontal" Spacing="10">
                <TextBlock Text="转出比例:" VerticalAlignment="Center" />
                <NumericUpDown Value="{Binding ManyToManyPercent}" 
                               Minimum="0.001" 
                               Maximum="100" 
                               Increment="1" 
                               FormatString="F4" 
                               Width="150" />
                <TextBlock Text="%" VerticalAlignment="Center" />
                <TextBlock Text="(保留0.0011 SOL作为手续费和账号留存)" VerticalAlignment="Center" Margin="10,0,0,0" />
            </StackPanel>
            
            <!-- 多转1模式参数 -->
            <StackPanel Grid.Row="0" Grid.ColumnSpan="4" 
                        IsVisible="{Binding IsManyToOne}"
                        Orientation="Horizontal" Spacing="10">
                <TextBlock Text="转出比例:" VerticalAlignment="Center" />
                <NumericUpDown Value="{Binding TransferPercent}"
                               Minimum="1" Maximum="100" Increment="1" Width="150" />
                <TextBlock Text="%" VerticalAlignment="Center" />
                <TextBlock Text="(保留0.0011 SOL作为手续费和账号留存)" VerticalAlignment="Center" Margin="10,0,0,0" />
            </StackPanel>
            
            <!-- 批量兑换模式参数 -->
            <StackPanel Grid.Row="0" Grid.ColumnSpan="4" 
                        IsVisible="{Binding IsExchange}"
                        Orientation="Horizontal" Spacing="10">
                <TextBlock Text="兑换类型:" VerticalAlignment="Center" />
                <ComboBox SelectedIndex="{Binding ExchangeTypeIndex}" Width="150">
                    <ComboBoxItem Content="SOL → USDC" />
                    <ComboBoxItem Content="USDC → SOL" />
                </ComboBox>
                <TextBlock Text="兑换比例:" VerticalAlignment="Center" Margin="10,0,0,0" />
                <NumericUpDown Value="{Binding ExchangePercent}" Minimum="1" Maximum="100" Increment="1" Width="150" />
                <TextBlock Text="%" VerticalAlignment="Center" />
            </StackPanel>
            
            <!-- 滑点设置 -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="滑点容忍度:" VerticalAlignment="Center" Margin="0,10,0,0" 
                       IsVisible="{Binding IsExchange}" />
            <NumericUpDown Grid.Row="1" Grid.Column="1" 
                           Value="{Binding Slippage}" 
                           Minimum="0.1" 
                           Maximum="100"
                           Increment="0.1" 
                           FormatString="F1"
                           Width="150" 
                           Margin="0,10,0,0"
                           IsVisible="{Binding IsExchange}" />
            <TextBlock Grid.Row="1" Grid.Column="2" Text="%" VerticalAlignment="Center" Margin="5,10,0,0" 
                       IsVisible="{Binding IsExchange}" />
            
            <!-- 批量转代币模式参数 -->
            <StackPanel Grid.Row="0" Grid.ColumnSpan="4" 
                        IsVisible="{Binding IsTokenSwap}"
                        Orientation="Horizontal" Spacing="10">
                <TextBlock Text="代币地址:" VerticalAlignment="Center" />
                <TextBox Text="{Binding TokenAddress}" Watermark="代币地址" Width="400"></TextBox>
                <TextBlock Text="代笔数量:" VerticalAlignment="Center" Margin="10,0,0,0" />
                <NumericUpDown Value="{Binding TokenCount}" Minimum="1" Maximum="1000000000" Increment="1000" Width="150" />
            </StackPanel>
        </Grid>
        
        <!-- 底部按钮 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0" Spacing="10">
            <Button Content="取消" Command="{Binding CancelCommand}" Width="100" />
            <Button Content="执行" Command="{Binding ExecuteCommand}" Width="100" Classes="accent" />
        </StackPanel>
    </Grid>
</Window>