using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Text;
////

namespace Shared.Utils {
    public class FileUtils {
        public static UTF8Encoding ENCODING_UTF8 = new UTF8Encoding(false);
        public static void test() {
            string filePath = ".file.bin"; // 二进制文件路径
            byte[] insertionData = { 0x11, 0x22, 0x33 }; // 要插入的数据字节数组
            int insertionIndex = 10; // 插入数据的位置索引

            FileUtils.InsertDataInBinaryFile(filePath, insertionData, insertionIndex);
        }

        public static string readAllTextSafe(string url, Encoding encode = null) {
            if (encode == null) { encode = Encoding.UTF8; }

            if (!File.Exists(url)) return string.Empty;

            using FileStream fs = new FileStream(url, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

            using StreamReader sr = new StreamReader(fs, encode);

            StringBuilder sb = new StringBuilder();

            while (!sr.EndOfStream) { sb.AppendLine(sr.ReadLine()); }

            return sb.ToString();
        }
        public static void writeAllTextSafe(string savePath, string dataFragment, Encoding encode = null) {
            if (!CreateDir(savePath)) { return; }

            if (string.IsNullOrEmpty(dataFragment)) { return; }
            if (encode == null) { encode = ENCODING_UTF8; }
            using FileStream fs = new FileStream(savePath, FileMode.OpenOrCreate, FileAccess.ReadWrite
                                               , FileShare.ReadWrite);
            using StreamWriter bw = new StreamWriter(fs,encode);
            bw.Write(dataFragment);
            bw.Flush();
        }

        public static List<string> readAllLinesSafe(string url, Encoding encode) {
            if (!File.Exists(url)) return new List<string>();

            try {
                using FileStream fs = new FileStream(url, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
                using StreamReader sr = new StreamReader(fs, encode);

                List<string> sb = new List<string>();

                while (!sr.EndOfStream) { sb.Add(sr.ReadLine()); }

                return sb;
            } catch (Exception e) {
                e.printStack();
                return new List<string>();
            }
        }

        public static bool TryCreateSafe(string path) {
            if (File.Exists(path)) { return true; }

            // 获取父目录路径
            string parentDirectory = Path.GetDirectoryName(path);
            // 创建父目录（如果不存在）
            if (!string.IsNullOrEmpty(parentDirectory)&&!Directory.Exists(parentDirectory)) {
                var dir = Directory.CreateDirectory(parentDirectory);
                if (dir == null) { return false; }
            }
            try {
                using var stream = File.Create(path);
                stream.Close();
                return true;
            } catch (Exception e) {
                e.printStack();
                return false;
            }
        }

        // 获取相对路径
        public static string GetRelativePath(string basePath, string fullPath) {
            string relativePath = fullPath.Replace(basePath, "").TrimStart(Path.DirectorySeparatorChar);
            return relativePath;
        }

        public static void InsertDataInBinaryFile(string filePath, byte[] data, int index) {
            byte[] originalData = File.ReadAllBytes(filePath); // 读取原始二进制文件数据

            byte[] newData = new byte[originalData.Length + data.Length]; // 创建新的字节数组

            // 复制插入位置之前的原始数据
            Array.Copy(originalData, 0, newData, 0, index);

            // 复制插入数据
            Array.Copy(data, 0, newData, index, data.Length);

            // 复制原始数据中插入位置之后的数据，并进行整体偏移
            Array.Copy(originalData, index, newData, index + data.Length, originalData.Length - index);

            // 将新的数据写入二进制文件
            File.WriteAllBytes(filePath, newData);

            Console.WriteLine("数据已成功插入到二进制文件中。");
        }

        public static bool CreateDir(string savePath) {
            var fileInfo = new FileInfo(savePath);

            if (!fileInfo.Directory.Exists) { fileInfo.Directory.Create(); }

            return true;
        }

        public static bool CreateFile(string savePath) {
            if (File.Exists(savePath)) { return true; }

            var fileInfo = new FileInfo(savePath);

            if (!fileInfo.Directory.Exists) { fileInfo.Directory.Create(); }

            File.Create(savePath).Close();
            return true;
        }

        public static byte[] readBytes(string file, int start, int len) { return readBytes(file, start, len); }

        public static byte[] readBytes(string file, long start, int len) {
            if (!File.Exists(file)) return null;

            using FileStream fs = new FileStream(file, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);

            if (fs.Length < start + len) { return null; }

            using BinaryReader br = new BinaryReader(fs);
            fs.Seek(start, SeekOrigin.Begin);
            return br.ReadBytes(len);
        }

        public static byte[] readAllBytes(string file) {
            if (!File.Exists(file)) return null;

            using FileStream fs = new FileStream(file, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);

            if (fs.Length > int.MaxValue) { return null; }

            using BinaryReader br = new BinaryReader(fs);
            fs.Seek(0, SeekOrigin.Begin);
            return br.ReadBytes((int)fs.Length);
        }

        public static void WriteBytes(string savePath, byte[] dataFragment, int start, int dataFragmentLength) {
            if (!CreateDir(savePath)) { return; }

            using FileStream fs = new FileStream(savePath, FileMode.OpenOrCreate, FileAccess.ReadWrite
                                               , FileShare.ReadWrite);
            using BinaryWriter bw = new BinaryWriter(fs);
            bw.Seek(start, SeekOrigin.Begin);
            bw.Write(dataFragment);
            bw.Flush();
        }

        public static void WriteAllBytesSafe(string savePath, byte[] responseData) {
            if (!TryCreateSafe(savePath)) { return; }

            if (!File.Exists(savePath)) { File.Create(savePath); }

            WriteBytes(savePath, responseData, 0, responseData.Length);
        }

        public static void WriteAllLineSafe(string savePath, List<string> contents) {
            if (!TryCreateSafe(savePath)) { return; }

            try {
                using FileStream fs = new FileStream(savePath, FileMode.OpenOrCreate, FileAccess.ReadWrite
                                                   , FileShare.ReadWrite);
                
                using StreamWriter writer = new StreamWriter(fs,ENCODING_UTF8);

                foreach (string line in contents) { writer.WriteLine(line); }

                writer.Flush();
            } catch (Exception e) { e.printStack(); }
        }

        // Token: 0x0600009E RID: 158 RVA: 0x000079A4 File Offset: 0x00005BA4
        public static bool CreateFixedSizeFile(string fileName, long fileSize) {
            if (string.IsNullOrEmpty(fileName)) { return false; }

            if (fileSize < 0L) { return false; }

            string directoryName = Path.GetDirectoryName(fileName);

            if (!string.IsNullOrEmpty(directoryName) && !Directory.Exists(directoryName)) {
                Directory.CreateDirectory(directoryName);
            }

            FileStream fileStream = null;

            try {
                fileStream = new FileStream(fileName, FileMode.Create);
                fileStream.SetLength(fileSize);
            } catch {
                if (fileStream != null) {
                    fileStream.Close();
                    File.Delete(fileName);
                }

                return false;
            } finally {
                if (fileStream != null) { fileStream.Close(); }
            }

            return true;
        }

        public static void GetAllSubFile(string directory, List<string> fileList) {
            try {
                //file
                string[] files = Directory.GetFiles(directory);

                foreach (string file in files) { fileList.Add(file); }

                //dir
                string[] subDirectories = Directory.GetDirectories(directory);

                foreach (string subDirectory in subDirectories) {
                    // 递归处理子目录
                    GetAllSubFile(subDirectory, fileList);
                }
            } catch (Exception ex) { Log.e("Error accessing directory: " + ex.Message); }
        }

        public static string Backup(string srcFileName) {
            if (!File.Exists(srcFileName)) { return string.Empty; }
            var dst = GetTempFileName(srcFileName);
            File.Copy(srcFileName, dst);
            return dst;
        }

        public static void DeleteAllFiles(string rootPath, string filterExp) {
            string[] metaFiles = Directory.GetFiles(rootPath, filterExp
                                                  , SearchOption.AllDirectories);

            foreach (var metaFile in metaFiles) { File.Delete(metaFile); }
        }

        public static string[] GetAllFiles(string rootPath, string fileNameSuffix) {
            return Directory.GetFiles(rootPath, fileNameSuffix, SearchOption.AllDirectories);
        }

        public static void Delete(string tempFile) {
            try { if(File.Exists(tempFile))File.Delete(tempFile); } catch (Exception e) { e.printStack(); }
        }

        public static void Move(string tempFile, string saveZipPath) {
            try { if(File.Exists(tempFile))File.Move(tempFile,saveZipPath); } catch (Exception e) { e.printStack(); }
        }

        public static string GetTempFileName(string srcFileName) {
            var time = $"_{DateTime.Now:yy-MM-dd_HH-mm-ss}";
            var file = new FileInfo(srcFileName);
            var ext = file.Extension;
            var dst = string.IsNullOrEmpty(ext)?$"{srcFileName}{time}":srcFileName.Replace(ext, time + ext);
            return dst;
        }
    }
    public static class ExpcetionExt {
        public static void printStack(this Exception e) {
            Log.e(e);
        }
    }
}
