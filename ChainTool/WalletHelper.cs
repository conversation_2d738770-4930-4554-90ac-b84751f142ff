using System;
using System.Text;
using Solnet.Wallet;
using Solnet.Rpc;
using Solnet.Wallet.Bip39;

namespace ChainTool
{
    /// <summary>
    /// 钱包初始化和管理辅助类
    /// 支持多种方式创建和初始化钱包
    /// </summary>
    public static class WalletHelper
    {

        /// <summary>
        /// 通过私钥和公钥创建 Account 对象（验证匹配性）
        /// </summary>
        /// <param name="privateKey">Base58 格式的私钥</param>
        /// <param name="publicKey">Base58 格式的公钥</param>
        /// <returns>Account 对象</returns>
        public static Account CreateAccountFromKeyPair(string privateKey, string publicKey)
        {
            if (string.IsNullOrEmpty(privateKey))
            {
                throw new ArgumentException("私钥不能为空", nameof(privateKey));
            }

            if (string.IsNullOrEmpty(publicKey))
            {
                throw new ArgumentException("公钥不能为空", nameof(publicKey));
            }

            try
            {
                // 先通过私钥创建账户
                var account = new Account(privateKey,publicKey);
                
                // 验证公钥是否匹配
                if (account.PublicKey.Key != publicKey)
                {
                    throw new ArgumentException("提供的公钥与私钥不匹配");
                }

                Console.WriteLine($"钱包初始化成功 - 地址: {account.PublicKey}");
                return account;
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"钱包初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成新的钱包
        /// </summary>
        /// <returns>包含助记词和账户信息的元组</returns>
        public static (Wallet wallet, string mnemonic) GenerateNewWallet()
        {
            try
            {
                var mnemonic = new Mnemonic(WordList.English, WordCount.Twelve);
                var wallet = new Wallet(mnemonic);
                
                Console.WriteLine($"生成新钱包成功:");
                Console.WriteLine($"  地址: {wallet.Account.PublicKey}");
                Console.WriteLine($"  助记词: {mnemonic}");
                
                return (wallet, mnemonic.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception($"生成新钱包失败: {ex.Message}");
            }
        }


        /// <summary>
        /// 验证公钥格式是否正确
        /// </summary>
        /// <param name="publicKey">Base58 格式的公钥</param>
        /// <returns>是否有效</returns>
        public static bool IsValidPublicKey(string publicKey)
        {
            if (string.IsNullOrEmpty(publicKey))
            {
                return false;
            }

            try
            {
                var pubKey = new PublicKey(publicKey);
                return pubKey != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取钱包余额
        /// </summary>
        /// <param name="rpcClient">RPC 客户端</param>
        /// <param name="account">账户</param>
        /// <returns>余额（以 lamports 为单位）</returns>
        public static async System.Threading.Tasks.Task<ulong> GetBalanceAsync(IRpcClient rpcClient, Account account)
        {
            try
            {
                var balance = await rpcClient.GetBalanceAsync(account.PublicKey);
                if (balance.WasSuccessful)
                {
                    return balance.Result.Value;
                }
                else
                {
                    throw new Exception($"获取余额失败: {balance.Reason}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"查询余额时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 打印钱包信息
        /// </summary>
        /// <param name="account">账户</param>
        /// <param name="rpcClient">可选的 RPC 客户端，用于查询余额</param>
        public static async System.Threading.Tasks.Task PrintWalletInfoAsync(Account account, IRpcClient rpcClient = null)
        {
            Console.WriteLine("=== 钱包信息 ===");
            Console.WriteLine($"公钥地址: {account.PublicKey}");
            Console.WriteLine($"私钥: {account.PrivateKey}");
            
            if (rpcClient != null)
            {
                try
                {
                    var balance = await GetBalanceAsync(rpcClient, account);
                    Console.WriteLine($"SOL 余额: {balance / 1_000_000_000.0:F4} SOL");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"获取余额失败: {ex.Message}");
                }
            }
            
            Console.WriteLine("================");
        }

        /// <summary>
        /// 钱包信息类
        /// </summary>
        public class WalletInfo
        {
            public string PublicKey { get; set; }
            public string PrivateKey { get; set; }
            public ulong Balance { get; set; }
            public string Mnemonic { get; set; }
        }

        /// <summary>
        /// 获取完整的钱包信息
        /// </summary>
        /// <param name="account">账户</param>
        /// <param name="rpcClient">RPC 客户端</param>
        /// <param name="mnemonic">可选的助记词</param>
        /// <returns>钱包信息对象</returns>
        public static async System.Threading.Tasks.Task<WalletInfo> GetWalletInfoAsync(Account account, IRpcClient rpcClient, string mnemonic = null)
        {
            var walletInfo = new WalletInfo
            {
                PublicKey = account.PublicKey.Key,
                PrivateKey = account.PrivateKey.Key,
                Mnemonic = mnemonic
            };

            try
            {
                walletInfo.Balance = await GetBalanceAsync(rpcClient, account);
            }
            catch
            {
                walletInfo.Balance = 0;
            }

            return walletInfo;
        }
    }
}
