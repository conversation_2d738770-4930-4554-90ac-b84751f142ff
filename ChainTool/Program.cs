using Avalonia;
using System;
using System.Collections.Generic;
using System.Text;
using Shared;
using Solnet.Metaplex.NFT.Library;
using Solnet.Metaplex.Utilities;
using Solnet.Programs;
using Solnet.Programs.PumpFunAmm;
using Solnet.Wallet;

namespace ChainTool;

sealed class Program {
    // Initialization code. Don't use any Avalonia, third-party APIs or any
    // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
    // yet and stuff might break.
    [STAThread]
    public static void Main(string[] args) {
        Log.loggers.Add(new LoggerFile());
        BuildAvaloniaApp()
           .StartWithClassicDesktopLifetime(args);
        Log.closeAndFlush();
    }

    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp() => AppBuilder.Configure<App>()
       .UsePlatformDetect()
       .WithInterFont()
       .LogToTrace();
}
